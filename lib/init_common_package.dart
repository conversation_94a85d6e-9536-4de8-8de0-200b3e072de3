// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:otp_autofill/otp_autofill.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:trust_vision_plugin/trust_vision_plugin.dart';
import 'package:uuid/uuid.dart';
import 'package:visibility_detector/visibility_detector.dart';

import 'data/http_client/common_http_client.dart';
import 'data/http_client/dio_http_client_impl.dart';
import 'data/http_client/dio_request_option_mapper.dart';
import 'data/repository/ekyc/ekyc_repo.dart';
import 'data/repository/ekyc/ekyc_repo_impl.dart';
import 'data/repository/logging/logging_repo.dart';
import 'data/repository/logging/logging_repo_impl.dart';
import 'feature/data_collection/data_collector.dart';
import 'feature/data_collection/data_collector_impl.dart';
import 'feature/data_collection/device_identifier/android_device_identifier/android_device_identifier_impl.dart';
import 'feature/data_collection/device_identifier/device_identifer_impl.dart';
import 'feature/data_collection/device_identifier/device_identifier.dart';
import 'feature/data_collection/device_identifier/ios_device_identifier/ios_device_identifier_impl.dart';
import 'feature/data_collection/firebase_performance/fp_network_request_interceptor.dart';
import 'feature/data_collection/ip_address_wrapper/ip_address_wrapper.dart';
import 'feature/data_collection/ip_address_wrapper/ip_address_wrapper_impl.dart';
import 'feature/data_collection/storage_info_wrapper/storage_info_wrapper_impl.dart';
import 'feature/ekyc/bridges/ekyc_bridge.dart';
import 'feature/ekyc/bridges/ekyc_bridge_impl.dart';
import 'feature/in_app_review/in_app_review_wrapper.dart';
import 'feature/in_app_review/in_app_review_wrapper_impl.dart';
import 'feature/in_app_update/in_app_update_wrapper.dart';
import 'feature/in_app_update/in_app_update_wrapper_impl.dart';
import 'feature/onesignal/listener_handler.dart';
import 'feature/server_logging/common_navigator_observer.dart';
import 'feature/server_logging/event_tracking_utils.dart';
import 'feature/server_logging/firebase_analytics.dart';
import 'feature/server_logging/firebase_crashlytics.dart';
import 'feature/webview/webview_utils.dart';
import 'flavors/flavor_config.dart';
import 'global_key_provider.dart';
import 'resources/global.dart';
import 'util/alert_manager.dart';
import 'util/clear_all_notifications_wrapper.dart';
import 'util/clipboard_wrapper.dart';
import 'util/common_toast.dart';
import 'util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'util/device_platform.dart';
import 'util/extension.dart';
import 'util/flutter_downloader/common_flutter_downloader.dart';
import 'util/flutter_downloader/common_flutter_downloader_impl.dart';
import 'util/flutter_downloader/flutter_downloader_wrapper.dart';
import 'util/flutter_downloader/utils/common_flutter_downloader_utils.dart';
import 'util/flutter_downloader/utils/isolate_name_server_wrapper.dart';
import 'util/functions.dart';
import 'util/local_storage_helper.dart';
import 'util/network_manager.dart';
import 'util/otp_auto_fill/otp_auto_fill.dart';
import 'util/otp_auto_fill/sms_otp_auto_fill_impl.dart';
import 'util/path_provider_wrapper/android_path_provider_wrapper.dart';
import 'util/path_provider_wrapper/path_provider_wrapper.dart';
import 'util/secure_storage_helper.dart';
import 'util/share_preference_helper.dart';
import 'util/shared_preferences_impl.dart';
import 'util/url_launcher_wrapper.dart';
import 'util/uuid/uuid_generator.dart';
import 'util/uuid/uuid_generator_impl.dart';
import 'widget/common_image_provider.dart';

GetIt getIt = GetIt.instance;

Future<void> initCommonPackage({Locale? locale}) async {
  getIt.registerLazySingleton<DevicePlatform>(() => DevicePlatformImp());
  getIt.registerLazySingletonAsync(() => PackageInfo.fromPlatform());
  await _registerDeviceInfoPlugin();

  await _initFirebase();

  await _initNetworkManager();

  _registerLocalStorages();

  _registerUtilFunction();

  await _registerHttpClient(locale: locale);

  /// The LoggingRepo needs to be registered after the CommonHttpClient and before the DataCollector
  /// because the LoggingRepo uses the HTTP client to call the log API.
  /// The DataCollector requires the LoggingRepo to log any error events that occur.
  _registerLoggingRepo();

  _registerUUIDGenerator();

  _registerEventTrackingUtils();

  /// The _registerDataCollector() method needs to be called after the #_registerLoggingRepo() method,
  /// because initializing the DataCollector requires the LoggingRepo to log any error events that occur.
  await _registerDataCollector();

  await initDataDog(DatadogSdk.instance);

  initOneSignal();

  _registerUiComponents();

  _registerSMSAutoFill();

  _registerCopyToClipboard();

  _registerUrlLauncher();

  _registerClearAllNotifications();

  _registerFlutterDownloader();

  _registerGlobalKeyProvider();

  _registerWebViewRelated();

  _initVisibilityDetector();

  _registerEkycCommonModule();

  _registerInAppUpdate();

  _registerInAppReview();
}

void _registerWebViewRelated() {
  getIt.registerLazySingleton<CommonWebViewUtils>(() => CommonWebViewUtils());
}

void _registerUtilFunction() {
  getIt.registerLazySingleton(() => CommonUtilFunction());
}

void _registerLocalStorages() {
  // Secure Storage
  getIt.registerLazySingleton<FlutterSecureStorage>(
      () => const FlutterSecureStorage(
          aOptions: AndroidOptions(encryptedSharedPreferences: true),
          // ignore: avoid_redundant_argument_values
          iOptions: IOSOptions.defaultOptions));
  getIt.registerLazySingleton<CommonLocalStorageHelper>(() =>
      CommonSecureStorageHelperImpl(
          secureStorage: getIt.get<FlutterSecureStorage>()));

  // Insecure Storage
  getIt.registerLazySingleton<CommonSharedPreferencesHelper>(
      () => CommonSharedPreferencesHelperImpl());
}

// Platform messages are asynchronous, so we initialize in an async method.
@visibleForTesting
Future<void> initOneSignal() async {
  final String? oneSignalAppId = FlavorConfig.instance.values.oneSignalAppId;
  if (oneSignalAppId == null) return;

  OneSignal.shared.setLogLevel(OSLogLevel.verbose, OSLogLevel.none);

  // disable automatically collect location
  // fix issue ANRs - https://trustingsocial1.atlassian.net/browse/EMA-5667
  // Native method - jdk.internal.misc.Unsafe.park
  OneSignal.shared.setLocationShared(false);

  await OneSignal.shared.setAppId(oneSignalAppId);

  // CHANGE THIS parameter to true if you want to test GDPR privacy consent
  // OneSignal.shared.setRequiresUserPrivacyConsent(_requireConsent);
  // bool requiresConsent = await OneSignal.shared.requiresUserPrivacyConsent();
  // bool userProvidedPrivacyConsent = await OneSignal.shared.userProvidedPrivacyConsent();
  // if (kDebugMode) {
  //   print("USER PROVIDED PRIVACY CONSENT: $userProvidedPrivacyConsent");
  // }

  // iOS-only method to open launch URLs in Safari when set to false
  OneSignal.shared.setLaunchURLsInApp(false);

  // Some examples of how to use In App Messaging public methods with OneSignal SDK
  // oneSignalInAppMessagingTriggerExamples();

  OneSignal.shared.disablePush(false);

  OneSignal.shared.setPermissionObserver((OSPermissionStateChanges changes) {
    if (kDebugMode) {
      print('PERMISSION STATE CHANGED: ${changes.jsonRepresentation()}');
    }
    if (changes.to.status == OSNotificationPermission.denied) {
      final LoggingRepo loggingRepo = getIt.get<LoggingRepo>();
      loggingRepo.logEvent(eventType: EventType.deniedNotification);
    }

    if (!getIt.isRegistered<OneSignalListenerHandler>()) return;
    final OneSignalListenerHandler oneSignalListenerHandler =
        getIt.get<OneSignalListenerHandler>();
    oneSignalListenerHandler.onPermissionObserver(changes);
  });

  OneSignal.shared
      .setSubscriptionObserver((OSSubscriptionStateChanges changes) async {
    if (kDebugMode) {
      print('SUBSCRIPTION STATE CHANGED: ${changes.jsonRepresentation()}');
    }
    final LoggingRepo loggingRepo = getIt.get<LoggingRepo>();
    loggingRepo.logEvent(
        eventType: EventType.notificationToken,
        data: <String, dynamic>{'token': changes.to.pushToken});

    if (!getIt.isRegistered<OneSignalListenerHandler>()) return;
    final OneSignalListenerHandler oneSignalListenerHandler =
        getIt.get<OneSignalListenerHandler>();
    oneSignalListenerHandler.onSubscriptionObserver(changes);
  });
}

Future<void> _initNetworkManager() async {
  getIt.registerLazySingleton<Connectivity>(() => Connectivity());
  final NetworkManager networkManager = NetworkManager();
  await networkManager.initialise();
  getIt.registerLazySingleton<NetworkManager>(() => networkManager);
}

Future<void> _initFirebase() async {
  if (!FlavorConfig.instance.values.initializeFirebaseSdk) return;

  // https://firebase.google.com/docs/crashlytics/get-started?platform=flutter
  // https://firebase.blog/posts/2022/07/whats-new-in-crashlytics-for-flutter/
  // Pass all uncaught errors from the framework to Crashlytics.
  FlutterError.onError = FirebaseCrashlyticsWrapper.instance.recordFlutterError;

  // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
  PlatformDispatcher.instance.onError = (Object error, StackTrace stack) {
    FirebaseCrashlyticsWrapper.instance.recordError(error, stack);
    return true;
  };

  // Firebase Analytics
  getIt.registerLazySingleton<FirebaseAnalyticsWrapper>(
      () => FirebaseAnalyticsWrapper(
            FirebaseAnalytics.instance,
            FirebaseAnalyticsObserver(analytics: FirebaseAnalytics.instance),
          ));
}

@visibleForTesting
Future<void> initDataDog(DatadogSdk sdk) async {
  //used datadog to support monitor user real time session
  final DatadogConfiguration? config =
      FlavorConfig.instance.values.commonDataDogConfig?.datadogConfiguration;
  if (config != null) {
    //only init datadog when config is provided
    await sdk.initialize(
      config,
      TrackingConsent
          .granted, //we already asked user to agree with term and condition https://docs.datadoghq.com/real_user_monitoring/mobile_and_tv_monitoring/flutter/setup?tab=rum#set-tracking-consent-gdpr-compliance
    );
    final String? deviceId = await getIt.get<DataCollector>().getDeviceId();
    sdk.setUserInfo(extraInfo: <String, String?>{'deviceId': deviceId});
    final DatadogLogger? logger = sdk.logs?.createLogger(
      DatadogLoggerConfiguration(),
    );
    if (logger != null) {
      final Map<String, String>? tags =
          FlavorConfig.instance.values.commonDataDogConfig?.logCustomTags;
      if (tags != null) {
        tags.forEach((String key, String val) {
          logger.addTag(key, val);
        });
      }
      getIt.registerLazySingleton<DatadogLogger>(() => logger);
    }
  }
}

void _registerUiComponents() {
  getIt.registerLazySingleton<AlertManager>(
      () => CommonToast(FlutterToastWrapper()));

  getIt.registerLazySingleton<CommonImageProvider>(
      () => CommonImageProviderImpl());

  // navigator observer
  getIt.registerLazySingleton<CommonNavigatorObserver>(
      () => CommonNavigatorObserver());
}

Future<void> _registerHttpClient({Locale? locale}) async {
  // Http client
  final Dio dio = await getDio(locale: locale);
  getIt.registerLazySingleton<Dio>(() => dio);

  getIt.registerLazySingleton<CommonHttpClient>(
    () => DioClientImpl(
      getIt.get<Dio>(),
      dioRequestOptionMapper: DioRequestOptionMapper(),
    ),
  );
}

Future<Dio> getDio({Locale? locale}) async {
  final PackageInfo packageInfo = await getIt.getAsync<PackageInfo>();
  final DeviceInfoPluginWrapper deviceInfoPluginWrapper =
      getIt.get<DeviceInfoPluginWrapper>();

  final Dio dio = Dio();
  dio.options.headers[HeaderKey.appVersion] = packageInfo.version;
  dio.options.headers[HeaderKey.timeZoneOffset] =
      DateTime.now().getTimeZoneOffset();
  dio.options.headers[HeaderKey.timeZone] = DateTime.now().timeZoneName;
  dio.options.headers[HeaderKey.appBuildNumber] = packageInfo.buildNumber;

  dio.options.headers[HeaderKey.deviceModel] =
      deviceInfoPluginWrapper.getDeviceModel();
  dio.options.headers[HeaderKey.platform] =
      deviceInfoPluginWrapper.getPlatformName();
  dio.options.headers[HeaderKey.osVersion] =
      deviceInfoPluginWrapper.getOSVersion();
  dio.options.headers[HeaderKey.language] = locale?.languageCode;

  final DevicePlatform platform = getIt.get<DevicePlatform>();
  if (platform.isAndroid()) {
    /// In Android OS, it's called build ID
    /// Refer to: https://source.android.com/docs/setup/about/build-numbers#source-code-tags-and-builds
    /// Refer for the format of the build ID: https://source.android.com/docs/setup/about/build-numbers#build-ids-defined
    /// I was only able to obtain the OS build number for the Android platform because
    /// I could not find an official document or library support to obtain it for iOS.
    dio.options.headers[HeaderKey.osBuildNumber] =
        deviceInfoPluginWrapper.getAndroidBuildNumber();
  }

  /// 1. When creating the first [Dio] instance in [_registerHttpClient], [DataCollector]
  ///    is not registered yet to get the device ID. This is because of a dependency issue:
  ///    [DataCollector] --> [LoggingRepo] --> [CommonHttpClient] --> [Dio].
  ///    Therefore, we need to add the device ID to the first [Dio] instance **later**
  ///    after the [DataCollector] is registered.
  /// 2. For subsequent [Dio] instances, [DataCollector] is already registered,
  ///    the device ID can be set right at the creation step of the [Dio] instance in [getDio].
  ///
  /// https://trustingsocial1.atlassian.net/browse/EMA-4687
  if (getIt.isRegistered<DataCollector>()) {
    final String? deviceId = await getIt.get<DataCollector>().getDeviceId();
    dio.options.headers[HeaderKey.deviceId] = deviceId;
  }

  if (kDebugMode) {
    print(dio.options.headers.toString());
  }

  dio.options.connectTimeout = const Duration(seconds: 10); // 10 seconds
  dio.options.receiveTimeout = const Duration(seconds: 10); // 10 seconds
  dio.options.sendTimeout = const Duration(seconds: 10); // 10 seconds
  dio.options.baseUrl = FlavorConfig.instance.values.baseUrl;

  //add this interceptor to monitor in firebase performance
  dio.interceptors.add(DioFirebasePerformanceInterceptor());
  return dio;
}

void _registerSMSAutoFill() {
  getIt.registerFactory<OtpAutoFill>(() => SmsOtpAutoFillImpl(OTPInteractor()));
}

void _registerCopyToClipboard() {
  getIt.registerLazySingleton<ClipboardWrapper>(() => ClipboardWrapper());
}

void _registerUrlLauncher() {
  getIt.registerLazySingleton<UrlLauncherWrapper>(() => UrlLauncherWrapper());
}

void _registerClearAllNotifications() {
  getIt.registerLazySingleton<ClearAllNotificationsWrapper>(
      () => ClearAllNotificationsWrapper());
}

void _registerFlutterDownloader() {
  getIt.registerFactory<CommonFlutterDownloader>(
      () => CommonFlutterDownloaderImpl(
            FlutterDownloaderWrapper(),
            CommonFlutterDownloaderUtils(
              pathProviderWrapper: PathProviderWrapper(),
              androidPathProviderWrapper: AndroidPathProviderWrapper(),
              devicePlatform: getIt.get<DevicePlatform>(),
            ),
            IsolateNameServerWrapper(),
          ));
}

void _registerGlobalKeyProvider() {
  getIt.registerSingleton<GlobalKeyProvider>(GlobalKeyProvider());
}

Future<void> _registerDataCollector() async {
  final DeviceIdentifier deviceIdentifier = DeviceIdentifierImpl(
    devicePlatform: getIt.get<DevicePlatform>(),
    androidDeviceIdentifier: AndroidDeviceIdentifierImpl(),
    iosDeviceIdentifier:
        IosDeviceIdentifierImpl(deviceInfo: getIt.get<DeviceInfoPlugin>()),
  );
  final IpAddressWrapper ipAddressWrapper = IpAddressWrapperImpl(
    networkInfo: NetworkInfo(),
    connectivity: getIt.get<Connectivity>(),
  );

  final DataCollector dataCollector = DataCollectorImpl(
    deviceIdentifier: deviceIdentifier,
    connectivity: getIt.get<Connectivity>(),
    storageInfoWrapper: StorageInfoWrapperImpl(),
    ipAddressWrapper: ipAddressWrapper,
    loggingRepo: getIt.get<LoggingRepo>(),
    firebaseAnalyticsWrapper: firebaseAnalyticsWrapper,
  );

  final String? deviceId = await dataCollector.getDeviceId();

  /// Set the device ID to the header of the HTTP client, which is used for Data Collection feature.
  /// Refer to: https://trustingsocial1.atlassian.net/browse/EMA-1349
  final Dio dio = getIt.get<Dio>();
  dio.options.headers[HeaderKey.deviceId] = deviceId;

  /// Set the device ID to the header of the Firebase Crashlytics, which is used for crash reporting.
  /// Refer to: https://trustingsocial1.atlassian.net/browse/EMA-5908
  if (FlavorConfig.instance.values.initializeFirebaseSdk) {
    FirebaseCrashlyticsWrapper.instance
        .setCustomKey(HeaderKey.deviceId, deviceId ?? '');
  }

  getIt.registerLazySingleton<DataCollector>(() => dataCollector);
}

void _registerLoggingRepo() {
  getIt.registerLazySingleton<LoggingRepo>(
    () => LoggingRepoImpl(commonHttpClient: getIt.get<CommonHttpClient>()),
  );
}

void _registerEventTrackingUtils() {
  getIt.registerLazySingleton<EventTrackingUtils>(() => EventTrackingUtils(
        deviceInfoPluginWrapper: getIt.get<DeviceInfoPluginWrapper>(),
        loggingRepo: getIt.get<LoggingRepo>(),
        uuidGenerator: getIt.get<UUIDGenerator>(),
      ));
}

void _registerUUIDGenerator() {
  getIt.registerLazySingleton<UUIDGenerator>(
      () => UUIDGeneratorImpl(const Uuid()));
}

Future<void> _registerDeviceInfoPlugin() async {
  getIt.registerLazySingleton(() => DeviceInfoPlugin());
  getIt.registerLazySingleton<DeviceInfoPluginWrapper>(
      () => DeviceInfoPluginWrapper(
            deviceInfo: getIt.get<DeviceInfoPlugin>(),
            platform: getIt.get<DevicePlatform>(),
          ));

  await getIt.get<DeviceInfoPluginWrapper>().initDeviceInfo();
}

void _initVisibilityDetector() {
  //check https://trustingsocial1.atlassian.net/wiki/spaces/Mobile/pages/3860529308/EMA-5414+Mobile+Technical+Optimize+page+visibility+detection+in+EVO+App
  VisibilityDetectorController.instance.updateInterval =
      Duration(milliseconds: 100);
}

Future<void> _registerEkycCommonModule() async {
  getIt.registerLazySingleton<EkycBridge>(
    () => EkycBridgeImpl(
      loggingRepo: getIt<LoggingRepo>(),
      trustVisionPlugin: TrustVisionPlugin.instance,
    ),
  );

  getIt.registerLazySingleton<EkycRepo>(
    () => EkycRepoImpl(
      getIt<CommonHttpClient>(),
    ),
  );
}

void _registerInAppUpdate() {
  getIt.registerLazySingleton<InAppUpdateWrapper>(
      () => InAppUpdateWrapperImpl());
}

void _registerInAppReview() {
  getIt.registerLazySingleton<InAppReviewWrapper>(
      () => InAppReviewWrapperImpl(inAppReview: InAppReview.instance));
}
