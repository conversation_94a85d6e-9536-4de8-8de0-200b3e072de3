// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

import '../../init_common_package.dart';
import '../../resources/resources.dart';
import '../back_button.dart';
import '../otp_listenable/otp_listenable_widget.dart';
import '../pin_code/common_pin_code_field_shape.dart';
import '../pin_code/common_pin_theme.dart';
import '../pin_code/pin_code_widget.dart';
import 'count_down_and_resend_otp_widget.dart';
import 'sub_title_and_phone_number_otp.dart';

class OtpWidget extends StatefulWidget {
  final String phoneNumber;
  final int resendInSec;
  final void Function(String)? onSubmit;
  final void Function(String)? onChange;
  final VoidCallback? onResendOtp;
  final String? errorText;

  final String? textOtpTitle;
  final String? textOtpDesc;
  final String? textOtpDoNotReceived;
  final String? textOtpResendText;
  final String? textOtpInvalidMsg;
  final String? textOtpLimit;
  final int? pinLength;
  final TextStyle? styleOtpLimit;
  final bool isClearOnTouch;
  final bool autoUnFocus;
  final bool autoFocus;
  final FocusNode? focusNode;
  final Widget? leading;
  final double? pinCodeTextFieldHeight;
  final double? pinCodeTextFieldWidth;
  final double spaceBetweenItems;
  final bool? showCursor;
  final Color? cursorColor;
  final double? cursorHeight;
  final double? cursorWidth;
  final TextStyle? textStyle;
  final BorderRadius? borderRadius;
  final CommonPinCodeFieldShape? shape;
  final Color? selectedBorderColor;
  final Alignment errorTextAlignment;
  final double? errorTextPaddingTop;

  const OtpWidget({
    required this.phoneNumber,
    required this.resendInSec,
    required this.onSubmit,
    required this.errorText,
    super.key,
    this.onResendOtp,
    this.textOtpTitle,
    this.textOtpDesc,
    this.textOtpDoNotReceived,
    this.textOtpInvalidMsg,
    this.textOtpResendText,
    this.onChange,
    this.pinLength,
    this.textOtpLimit,
    this.styleOtpLimit,
    this.focusNode,
    this.autoFocus = true,
    this.isClearOnTouch = true,
    this.autoUnFocus = false,
    this.leading,
    this.pinCodeTextFieldHeight,
    this.pinCodeTextFieldWidth,
    this.spaceBetweenItems = 8,
    this.showCursor,
    this.cursorColor,
    this.cursorHeight,
    this.cursorWidth,
    this.textStyle,
    this.borderRadius,
    this.shape,
    this.selectedBorderColor,
    this.errorTextAlignment = Alignment.centerLeft,
    this.errorTextPaddingTop,
  });

  @override
  State<OtpWidget> createState() => _OtpWidgetState();
}

class _OtpWidgetState extends State<OtpWidget> {
  final TextEditingController _textInputCodeController =
      TextEditingController();
  final CommonColors commonColors = getIt.get<CommonColors>();
  final CommonTextStyles commonTextStyles = getIt.get<CommonTextStyles>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          leading: Transform.translate(
              offset: const Offset(5, 0), // margin left 5
              child: widget.leading ?? const CommonBackButton()),
          backgroundColor: commonColors.background,
          elevation: 0),
      backgroundColor: commonColors.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(20, 5, 20, 8),
          child: Column(
            children: <Widget>[
              Align(
                  alignment: Alignment.centerLeft,
                  child: Text(widget.textOtpTitle ?? CommonStrings.otpTitle,
                      style: commonTextStyles.h600(commonColors.textActive))),
              const SizedBox(height: 8),
              widget.textOtpLimit?.isNotEmpty == true
                  ? _itemErrorLimitOtp()
                  : _itemBody()
            ],
          ),
        ),
      ),
    );
  }

  bool get isError => widget.errorText?.isNotEmpty == true;

  Widget _itemBody() => Column(children: <Widget>[
        SubTitleAndPhoneNumberOtpWidget(
          phoneNumber: widget.phoneNumber,
          textOtpDesc: widget.textOtpDesc,
          commonColors: commonColors,
          commonTextStyles: commonTextStyles,
        ),
        const SizedBox(height: 24),
        OtpListenableWidget(
          onOtpCodeReceived: (String code) {
            _textInputCodeController.text = code;
          },
          child: CommonPinCode(
            focusNode: widget.focusNode,
            autoFocus: widget.autoFocus,
            isClearOnTouch: widget.isClearOnTouch,
            autoUnFocus: widget.autoUnFocus,
            pinLength: widget.pinLength,
            textController: _textInputCodeController,
            onSubmit: widget.onSubmit,
            onChange: widget.onChange,
            showCursor: widget.showCursor ?? false,
            cursorColor: widget.cursorColor,
            cursorHeight: widget.cursorHeight,
            cursorWidth: widget.cursorWidth,
            textStyle: widget.textStyle,
            pinTheme: CommonPinTheme(
              fieldHeight: widget.pinCodeTextFieldHeight,
              fieldWidth: widget.pinCodeTextFieldWidth,
              inactiveColor: isError
                  ? commonColors.error
                  : commonColors.inputUnfocusedColor,
              activeColor: isError
                  ? commonColors.error
                  : commonColors.inputUnfocusedColor,
              selectedColor: widget.selectedBorderColor,
              borderRadius: widget.borderRadius,
              shape: widget.shape,
            ),
            spaceBetweenItems: widget.spaceBetweenItems,
          ),
        ),
        if (isError) const SizedBox(height: widget.errorTextPaddingTop ?? 0),
        _buildErrorMessage(),
        const SizedBox(height: 28),
        CountDownAndResendOtpWidget(
          commonColors: commonColors,
          commonTextStyles: commonTextStyles,
          resendInSec: widget.resendInSec,
          onResendOtp: _onTapResendOtp,
          textOtpResendText: widget.textOtpResendText,
          textOtpDoNotReceived: widget.textOtpDoNotReceived,
        ),
      ]);

  Widget _itemErrorLimitOtp() => Text(widget.textOtpLimit ?? '',
      style: widget.styleOtpLimit ??
          commonTextStyles.bodyLarge(commonColors.error));

  Widget _buildErrorMessage() {
    return Align(
      alignment: widget.errorTextAlignment,
      child: isError
          ? Text(widget.errorText!,
              style: commonTextStyles.bodySmall(color: commonColors.error))
          : const SizedBox(),
    );
  }

  void _onTapResendOtp() {
    ///do nothing if resend otp callback is null
    if (widget.onResendOtp == null) {
      return;
    }

    _textInputCodeController.clear();
    widget.onResendOtp?.call();
  }
}
