// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

// ignore_for_file: avoid_catches_without_on_clauses

import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';

import '../../data/repository/logging/log_error_mixin.dart';
import '../../data/repository/logging/logging_repo.dart';
import '../../data/request/mobile_data_collection_request.dart';
import '../../util/extension.dart';
import '../server_logging/firebase_analytics.dart';
import 'data_collector.dart';
import 'device_identifier/device_identifier.dart';
import 'ip_address_wrapper/ip_address_wrapper.dart';
import 'model/data_connection_type.dart';
import 'model/ip_address_info.dart';
import 'model/storage_info.dart';
import 'storage_info_wrapper/storage_info_wrapper.dart';

class DataCollectorImpl extends DataCollector with LogErrorMixin {
  final DeviceIdentifier deviceIdentifier;
  final StorageInfoWrapper storageInfoWrapper;
  final Connectivity connectivity;
  final IpAddressWrapper ipAddressWrapper;
  final FirebaseAnalyticsWrapper firebaseAnalyticsWrapper;

  final LoggingRepo loggingRepo;

  DataCollectorImpl({
    required this.deviceIdentifier,
    required this.storageInfoWrapper,
    required this.connectivity,
    required this.ipAddressWrapper,
    required this.loggingRepo,
    required this.firebaseAnalyticsWrapper,
  });

  @override
  Future<void> logMobileDataCollection() async {
    final List<dynamic> results = await Future.wait<dynamic>(<Future<dynamic>>[
      getDataConnectionType(),
      getStorageInfo(),
      getScreenSize(),
      getIpAddress(),
    ]);
    final MobileDataCollectionRequest request = createMobileDataCollectionRequest(results);
    loggingRepo.logEvent(eventType: EventType.mobileDataCollection, data: request.toJson());
  }

  @override
  Future<String?> getDeviceId() async {
    return deviceIdentifier.getDeviceId();
  }

  @override
  Future<StorageInfo> getStorageInfo() async {
    return storageInfoWrapper.getStorageInfo();
  }

  @override
  Future<List<DataConnectionType>?> getDataConnectionType() async {
    try {
      final List<ConnectivityResult> connectivityResults = await connectivity.checkConnectivity();
      final List<DataConnectionType> dataConnectionTypes = connectivityResults
          .map((ConnectivityResult connectivityResult) => DataConnectionType.fromConnectivityResult(
                connectivityResult,
              ))
          .toList();
      return dataConnectionTypes;
    } catch (err) {
      logPlatformErrorEvent(
        errorType: 'connectivity',
        action: 'get_data_connection_type',
        error: err,
      );
      return null;
    }
  }

  @override
  Future<IpAddressInfo?> getIpAddress() {
    return ipAddressWrapper.getIpAddressInfo();
  }

  @override
  Future<Size?> getScreenSize() async {
    final Iterable<FlutterView> views = WidgetsBinding.instance.platformDispatcher.views;
    if (views.isEmpty) {
      return null;
    }
    final FlutterView view = views.first;
    return view.physicalSize;
  }

  @visibleForTesting
  MobileDataCollectionRequest createMobileDataCollectionRequest(List<dynamic> results) {
    List<DataConnectionType>? dataConnectionType;
    StorageInfo? storageInfo;
    Size? screenResolution;
    IpAddressInfo? ipAddressInfo;

    for (final dynamic element in results) {
      if (element is List<DataConnectionType>) {
        dataConnectionType = element;
        continue;
      }

      if (element is StorageInfo) {
        storageInfo = element;
        continue;
      }

      if (element is Size) {
        screenResolution = element;
        continue;
      }

      if (element is IpAddressInfo) {
        ipAddressInfo = element;
        continue;
      }
    }

    return MobileDataCollectionRequest(
      dataConnectionType: dataConnectionType,
      storageInfo: storageInfo,
      screenResolution: screenResolution,
      ipAddressInfo: ipAddressInfo,
    );
  }

  /// Retrieves the Firebase Analytics app_instance_id, that is identifies a single installation of the app.
  @override
  Future<String?> getFirebaseAppInstanceId() async {
    try {
      return await firebaseAnalyticsWrapper.getAppInstanceId();
    } on Exception catch (e) {
      commonLog('Error when get Firebase App Instance Id: $e');
      return null;
    }
  }
}
