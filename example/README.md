# Flutter Common Package Example

This example demonstrates how to use the Flutter Common Package in a Flutter application.

> **Note**: This package is proprietary software owned by Trusting Social and is intended for internal use only within Trusting Social and its authorized partners and clients.

## Getting Started

1. Make sure you have Flutter installed and set up on your machine.
2. Clone this repository.
3. Run `flutter pub get` to install dependencies.
4. Run the example app with `flutter run`.

## What This Example Demonstrates

This example demonstrates the following components from the Flutter Common Package:

### Initialization

```dart
// Initialize flavor configuration
FlavorConfig(
  flavor: 'stag',
  values: CommonFlavorValues(
    baseUrl: 'https://api.example.com',
    initializeFirebaseSdk: false,
    oneSignalAppId: null,
  ),
);

// Initialize the common package
await initCommonPackage();
```

### Common Widgets

#### Buttons

```dart
CommonButton(
  onPressed: () {
    debugPrint('Primary button pressed');
  },
  style: buttonStyles.primaryButtonStyle,
  child: Text(
    'Primary Button',
    style: textStyles.buttonText(colors.background),
  ),
)
```

#### Radio Buttons

```dart
CommonRadio<String>(
  title: 'Option 1',
  value: 'option1',
  isSelected: _selectedOption == 'option1',
  onChange: (value) {
    setState(() {
      _selectedOption = value;
    });
  },
)
```

### Design System

The example demonstrates how to access and use the design system components:

```dart
final CommonButtonStyles buttonStyles = getIt.get<CommonButtonStyles>();
final CommonColors colors = getIt.get<CommonColors>();
final CommonTextStyles textStyles = getIt.get<CommonTextStyles>();
```

## Structure

- `lib/main.dart`: The main entry point of the example app.

## Screenshots

(Add screenshots here once the example is running)

## Next Steps

- Try modifying the example to use other components from the Flutter Common Package.
- Explore the source code to understand how the components are implemented.
- Check the documentation for more information on available components and their usage.
