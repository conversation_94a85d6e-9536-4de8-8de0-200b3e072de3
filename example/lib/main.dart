// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';

import 'extensions.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize flavor configuration
  FlavorConfig(
    flavor: 'stag',
    values: CommonFlavorValues(
      baseUrl: 'https://api.example.com',
      initializeFirebaseSdk: false,
      oneSignalAppId: null,
    ),
  );

  // Initialize the common package
  await initCommonPackage();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Common Package Example',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const HomePage(),
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String _selectedOption = 'option1';

  @override
  Widget build(BuildContext context) {
    final CommonButtonStyles buttonStyles = getIt.get<CommonButtonStyles>();
    final CommonColors colors = getIt.get<CommonColors>();
    final CommonTextStyles textStyles = getIt.get<CommonTextStyles>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Common Package Example'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              'Common Widgets Demo',
              style: textStyles.heading1(colors.textActive),
            ),
            const SizedBox(height: 24),

            // Common Button Examples
            Text(
              'Buttons',
              style: textStyles.heading2(colors.textActive),
            ),
            const SizedBox(height: 16),
            CommonButton(
              onPressed: () {
                debugPrint('Primary button pressed');
              },
              style: buttonStyles.primaryButtonStyle,
              child: Text(
                'Primary Button',
                style: textStyles.buttonText(colors.background),
              ),
            ),
            const SizedBox(height: 8),
            CommonButton(
              onPressed: () {
                debugPrint('Secondary button pressed');
              },
              style: buttonStyles.secondaryButtonStyle,
              child: Text(
                'Secondary Button',
                style: textStyles.buttonText(colors.primary),
              ),
            ),
            const SizedBox(height: 8),
            CommonButton(
              onPressed: null, // Disabled button
              style: buttonStyles.primaryButtonStyle,
              child: Text(
                'Disabled Button',
                style: textStyles.buttonText(colors.background),
              ),
            ),
            const SizedBox(height: 24),

            // Common Radio Examples
            Text(
              'Radio Buttons',
              style: textStyles.heading2(colors.textActive),
            ),
            const SizedBox(height: 16),
            CommonRadio<String>(
              title: 'Option 1',
              value: 'option1',
              isSelected: _selectedOption == 'option1',
              onChange: (String value) {
                setState(() {
                  _selectedOption = value;
                });
              },
            ),
            const SizedBox(height: 8),
            CommonRadio<String>(
              title: 'Option 2',
              value: 'option2',
              isSelected: _selectedOption == 'option2',
              onChange: (String value) {
                setState(() {
                  _selectedOption = value;
                });
              },
            ),
            const SizedBox(height: 8),
            CommonRadio<String>(
              title: 'Disabled Option',
              value: 'option3',
              isSelected: _selectedOption == 'option3',
              enable: false,
              onChange: (String value) {
                setState(() {
                  _selectedOption = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }
}
