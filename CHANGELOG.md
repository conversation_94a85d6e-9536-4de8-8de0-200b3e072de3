# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## v4.x.x

### [4.8.0]

#### Added
- [EMA-6720] Implemented flag to identify EVO App webview in WebViewController and WebView components
- [ENBCC-75] Added new common AppBar component for consistent UI across the application

#### Enhanced
- [ENBCC-69] Enhanced PIN text field with customizable border radius and border builders

### [4.7.0]

#### Added
- [EMA-6322] Integrated Datadog RUM SDK for improved monitoring

### [4.6.0]

#### Added
- [EMA-6052] Created common module for In-app updates and implemented with host app

#### Changed
- [EMA-6158] Implemented recommendations to optimize build time
- [EMA-6494] Updated UI on Checkout page (v4.6.1)

#### Improved
- [EMA-6026] Added calculation and reporting of test coverage for changed files

### [4.5.0]

#### Added
- [EMA-6028] Enforced 80% test coverage for code changes in new PRs

### [4.4.0]

#### Added
- [EMA-5908] Added Device ID to Firebase Crashlytics reports on Flutter Apps
- [EMA-6007] Implemented Firebase Performance for measuring network requests (v4.4.1)

#### Fixed
- Fixed quotes in CI/CD configuration
- [EMA-6007] Fixed issues with Firebase Performance network request measurements (v4.4.2)

### [4.3.0]

#### Added
- Added support for Firebase Performance monitoring
- Improved error handling in network requests

### [4.2.0]

#### Added
- Added new UI components for improved user experience
- Enhanced theming capabilities

#### Fixed
- Fixed issues with WebView implementation (v4.2.4)
- Resolved memory leaks in certain widgets (v4.2.4)

### [4.1.0]

#### Added
- Enhanced analytics capabilities
- Improved error reporting

### [4.0.0]

#### Changed
- Major version update with breaking changes
- [EMA-4728] Upgraded firebase related libraries
- [EMA-151] Refactored logic code, prevented showing duplicate messages (Toast and SnackBar)
- [EMA-5192] Added functionality to store customer location on DOE

#### Added
- [EMA-5178] Improved unit tests for prepareForAppInitiation

#### Fixed
- [Hotfix] Allowed ethernet for checkOnlineNetwork
- [EMA-5080] Updated to support gradle 8.4
- [EMA-5197] Fixed wrong timezone string when device using negative timezone

## v3.x.x

### [3.0.0]

#### Changed
- Complete redesign of the package structure
- Improved API for better developer experience
- Enhanced performance across all components
- [EMA-4728] Upgraded firebase related libraries

## v2.x.x

### [2.8.0]

#### Changed
- [EMA-4712] Upgraded Flutter version to 3.24
- [EMA-4723] Upgraded flutter_inappwebview library (v2.8.1)

### [2.7.0]

#### Added
- [EMA-4528] EVO App Data Tracking for NFC Flow

### [2.6.0]

#### Added
- Enhanced UI components
- Improved performance for large data sets

### [2.5.0]

#### Added
- [EMA-3726] Added onDidPopWebView to handle did pop WebView

### [2.4.0]

#### Fixed
- [EMA-2862] Fixed deprecated in CommonSelectionController when upgrading to flutter v3.22.2

### [2.3.0]

#### Added
- [EMA-3596] Added event to listen when a webview is disposed
- [EMA-3180] Changed time-out config on DOP native

#### Fixed
- [EMA-2618] Fixed keypad not hidden when user clicks on close button on input OTP screen
- [CI/CD] Added suffix vault role name

#### Improved
- [EMA-3328] Added upload build to Firebase App Distribution

### [2.2.0]

#### Changed
- [EMA-3382] Updated common dialog to handle spacing

### [2.1.0]

#### Added
- Enhanced error handling
- Improved logging capabilities

### [2.0.0]

#### Changed
- [EMA-2862] Upgraded flutter version to 3.22.1
- Major version update with significant improvements
- Refactored core components for better maintainability
- Updated dependencies to latest versions

## v1.x.x

### [1.13.0]

#### Changed
- [EMA-2711] Upgraded Firebase SDK

#### Fixed
- [EMA-2618] Fixed keypad not hidden when user clicks on close button on input OTP screen (v1.13.4)

### [1.12.0]

#### Added
- [EMA-1918] Native DOP open to custom OTP widget

### [1.11.0]

#### Added
- [EMA-1715] Created the foundation for Event Tracking feature

### [1.10.0]

#### Changed
- [EMA-1705] Upgraded 3rd party libraries with breaking changes

### [1.9.0]

#### Changed
- [EMA-1557] Upgraded Flutter to v3.13.8

### [1.8.0]

#### Changed
- [Mobile] Refactored using evoNavigatorKey.currentContext to pop() a page or a popup

### [1.7.0]

#### Fixed
- [EMA-1427] Fixed bug that cannot download with link on Android

### [1.6.0]

#### Changed
- [EMA-1381] Changed method name to clearDataOnTokenInvalid to be more accurate

### [1.5.0]

#### Added
- [EMA-1005] Added handling of New Action Types for Notification Navigation

### [1.4.0]

#### Changed
- [EMA-1145] Moved the showing notification logic to host app
- Removed redundant code

### [1.3.0]

#### Added
- [EMA-832] Enabled mocking in Http client

### [1.2.0]

#### Added
- Enhanced error handling
- Improved performance for network operations

### [1.1.0]

#### Added
- [EMA-461] Added support for display campaign's earn action and voucher's burn action

### [1.0.0]

#### Added
- Initial release of the Flutter Common Package
- Base classes for screens and BLoC architecture
- Data layer with HTTP clients and repositories
- Common features (eKYC, webview, OneSignal integration)
- Environment configuration for different flavors
- UI resources (colors, styles, dimensions)
- Reusable widgets
- Utility functions
