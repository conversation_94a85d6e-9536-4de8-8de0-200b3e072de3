# Flutter Common Package

This package provides a collection of reusable code and components for multiple Flutter projects within the company. It aims to streamline development, maintain consistency, and promote best practices across all projects.

[![Flutter Version](https://img.shields.io/badge/Flutter-3.16.0+-blue.svg)](https://flutter.dev/)
[![Dart Version](https://img.shields.io/badge/Dart-3.5.4+-blue.svg)](https://dart.dev/)
[![License: Proprietary](https://img.shields.io/badge/License-Proprietary-red.svg)]()

## Table of Contents

- [Getting Started](#getting-started)
- [Installation](#installation)
- [Initialization](#initialization)
- [Usage](#usage)
- [Components](#components)
  - [Base](#base)
  - [Data](#data)
  - [Feature](#feature)
  - [Flavors](#flavors)
  - [Resources](#resources)
  - [Widgets](#widgets)
  - [Utils](#utils)
- [Example](#example)
- [Documentation](#documentation)
- [Testing](#testing)
- [CI/CD](#cicd)
- [Contributing](#contributing)
- [License](#license)

## Getting Started

To get started with the Flutter Common Package, follow the installation steps below and refer to the usage guide for integrating the components into your Flutter projects.

## Installation

To add the Flutter Common Package to your project, follow these steps:

1. Add the following line to your `pubspec.yaml` file under the `dependencies` section:

```yaml
dependencies:
  # Flutter common package of Trusting Social Mobile team
  flutter_common_package:
    git:
      url: https://github.com/tsocial/flutter-common-package.git
      ref: your_ref_tag # e.g. 1.0.0
```

2. Run the following command to install the package:

```
flutter pub get
```

## Initialization

Before using the components from the Flutter Common Package, you need to initialize it in your app:

```dart
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize flavor configuration
  FlavorConfig(
    flavor: 'dev', // or 'staging', 'uat', 'prod'
    values: CommonFlavorValues(
      baseUrl: 'https://api.example.com',
      initializeFirebaseSdk: true, // Set to false if you don't want to initialize Firebase
      oneSignalAppId: 'your-onesignal-app-id', // Optional
      // Add other configuration values as needed
    ),
  );

  // Initialize the common package
  await initCommonPackage();

  runApp(const MyApp());
}
```

## Usage

To use the components from the Flutter Common Package, import the relevant files in your Dart code:

```dart
// Import specific components
import 'package:flutter_common_package/widget/widgets.dart'; // For UI widgets
import 'package:flutter_common_package/resources/resources.dart'; // For design system resources
import 'package:flutter_common_package/util/utils.dart'; // For utility functions

// Access components using dependency injection
final CommonButtonStyles buttonStyles = getIt.get<CommonButtonStyles>();
final CommonColors colors = getIt.get<CommonColors>();
final CommonTextStyles textStyles = getIt.get<CommonTextStyles>();

// Use components in your UI
CommonButton(
  onPressed: () {
    // Handle button press
  },
  style: buttonStyles.primaryButtonStyle,
  child: Text('Primary Button', style: textStyles.buttonText(colors.background)),
)
```

## Components

The Flutter Common Package includes the following components:

### Base

Contains base screen and bloc classes to be inherited by the projects.

```dart
// Example of using a base screen
class MyScreen extends PageBase {
  @override
  final RouteSettings routeSettings;

  @override
  final EventTrackingScreenId eventTrackingScreenId = EventTrackingScreenId.myScreen;

  const MyScreen({required this.routeSettings, super.key});

  @override
  State<StatefulWidget> createState() => _MyScreenState();
}
```

### Data

Includes a base HTTP client, common repository, and response & request entities.

```dart
// Example of using the HTTP client
final CommonHttpClient httpClient = getIt.get<CommonHttpClient>();

// Make a GET request
final response = await httpClient.get<Map<String, dynamic>>(
  '/endpoint',
  queryParameters: {'param': 'value'},
);
```

### Feature

Provides features that are used across various projects, such as:

- OneSignal integration for push notifications
- WebView implementation
- eKYC integration
- Data collection and analytics
- Server logging

### Flavors

Contains configuration files for different environments (Development, Staging, UAT, Production).

```dart
// Access the current flavor configuration
final String currentFlavor = FlavorConfig.instance.flavor;
final String baseUrl = FlavorConfig.instance.values.baseUrl;
```

### Resources

Consists of colors, button styles, text styles, UI strings, formats, and constants.

```dart
// Access design system resources
final CommonColors colors = getIt.get<CommonColors>();
final CommonTextStyles textStyles = getIt.get<CommonTextStyles>();
final CommonButtonStyles buttonStyles = getIt.get<CommonButtonStyles>();

// Use in widgets
Text(
  'Hello World',
  style: textStyles.heading1(colors.textActive),
)
```

### Widgets

A collection of custom widgets to maintain consistency in UI design across projects:

- CommonButton
- CommonRadio
- CommonAppBar
- CommonDialog
- OTP widgets
- Loading indicators
- And many more

### Utils

A set of utility functions to perform common operations, such as:

- Date formatting
- Data validation
- Network connectivity management
- Permission handling
- Local storage
- Secure storage
- Downloader
- Navigator
- And many more

## Example

Check out the [example](./example) directory for a complete example of how to use the Flutter Common Package in a Flutter application.

To run the example:

```bash
cd example
flutter pub get
flutter run
```

## Documentation

Comprehensive documentation is available for all components in the Flutter Common Package. The documentation follows the [Documentation Style Guide](./documents/DOCUMENTATION_STYLE_GUIDE.md).

To generate the API documentation:

```bash
dart doc .
```

This will create documentation in the `doc/api` directory.

## Testing

The Flutter Common Package includes comprehensive unit tests to ensure the quality and reliability of the code. To run the tests:

```bash
flutter test
```

To run the tests with coverage:

```bash
flutter test --coverage
```

## CI/CD

The Flutter Common Package includes GitHub workflows for continuous integration and deployment:

### Pull Request Workflow

When you create a pull request to `master`, `release/*`, or `hotfix/*` branches, the following checks are automatically run:

- Code analysis with `flutter analyze`
- Unit tests with code coverage
- Coverage verification on modified files

```bash
# The workflow runs these commands automatically
flutter analyze
flutter test --coverage
```

### Build Workflows

The repository includes reusable workflows for building and deploying Flutter applications:

- **Android Build Workflow**: Builds Android apps and deploys to Play Store or Firebase App Distribution
- **iOS Build Workflow**: Builds iOS apps and deploys to TestFlight

These workflows handle:

- Setting up the build environment
- Running tests in parallel with the build
- Code signing and provisioning
- Generating release builds
- Uploading to distribution platforms
- Notifying team members of build status

To use these workflows in your project, reference them in your own GitHub Actions configuration.

## Contributing

We encourage contributions from all team members. If you have any improvements, bug fixes, or new features, please follow these steps:

1. Create a new branch for your changes.
2. Add tests for your changes.
3. Ensure all tests pass.
4. Add documentation for your changes.
5. Commit your changes and push them to your fork.
6. Submit a pull request with a description of your changes.

Please make sure your code follows the [Documentation Style Guide](./documents/DOCUMENTATION_STYLE_GUIDE.md) and passes all tests.

## License

This Flutter Common Package is proprietary software owned by Trusting Social. It is intended for internal use only within Trusting Social and its authorized partners and clients. See the [LICENSE](./LICENSE) file for details.