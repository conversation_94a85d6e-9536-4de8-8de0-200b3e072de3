import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockDeviceInfoPlugin extends Mock implements DeviceInfoPlugin {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

class MockAndroidDeviceInfo extends Mock implements AndroidDeviceInfo {}

class MockIosDeviceInfo extends Mock implements IosDeviceInfo {}

class MockAndroidBuildVersion extends Mock implements AndroidBuildVersion {}

void main() {
  late DeviceInfoPlugin mockDeviceInfoPlugin;
  late DevicePlatform mockDevicePlatform;
  late DeviceInfoPluginWrapper deviceInfoPluginWrapper;

  const String testId = 'test_device_id';
  final IosDeviceInfo iosInfo = IosDeviceInfo.fromMap(
    <String, dynamic>{
      'name': 'iPhone 12',
      'systemName': 'systemName',
      'systemVersion': 'systemVersion',
      'model': 'model',
      'localizedModel': 'localizedModel',
      'isPhysicalDevice': false,
      'utsname': <String, dynamic>{
        'sysname': 'sysname',
        'nodename': 'nodename',
        'release': 'release',
        'version': 'version',
        'machine': 'machine',
      },
      'identifierForVendor': testId,
      'modelName': 'modelName',
      'isiOSAppOnMac': false,
    },
  );

  final AndroidDeviceInfo androidDeviceInfo30 = AndroidDeviceInfo.fromMap(<String, dynamic>{
    'version': <String, dynamic>{
      'sdkInt': 30,
      'baseOS': 'baseOS',
      'codename': 'codename',
      'incremental': 'incremental',
      'previewSdkInt': 30,
      'release': 'release',
      'securityPatch': 'securityPatch',
    },
    'board': '.board',
    'bootloader': '.bootloader',
    'brand': '.brand',
    'device': '.device',
    'display': '.display',
    'fingerprint': '.fingerprint',
    'hardware': '.hardware',
    'host': '.host',
    'id': '.id',
    'manufacturer': '.manufacturer',
    'model': '.model',
    'product': '.product',
    'supported32BitAbis': <String>[],
    'supported64BitAbis': <String>[],
    'supportedAbis': <String>[],
    'tags': '.tags',
    'type': '.type',
    'isPhysicalDevice': false,
    'systemFeatures': <String>[],
    'serialNumber': 'serialNumber',
    'isLowRamDevice': false,
  });

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getIt.registerSingleton<DeviceInfoPlugin>(MockDeviceInfoPlugin());
    mockDeviceInfoPlugin = getIt.get<DeviceInfoPlugin>();

    getIt.registerSingleton<DevicePlatform>(MockDevicePlatform());
    mockDevicePlatform = getIt.get<DevicePlatform>();
  });

  setUp(() {
    deviceInfoPluginWrapper = DeviceInfoPluginWrapper(
      deviceInfo: mockDeviceInfoPlugin,
      platform: mockDevicePlatform,
    );

    when(() => mockDeviceInfoPlugin.iosInfo).thenAnswer((_) {
      return Future<IosDeviceInfo>.value(iosInfo);
    });

    when(() => mockDeviceInfoPlugin.androidInfo).thenAnswer((_) {
      return Future<AndroidDeviceInfo>.value(androidDeviceInfo30);
    });
  });

  void mockDeviceIsAndroid() {
    when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
    when(() => mockDevicePlatform.isIOS()).thenReturn(false);
  }

  void mockDeviceIsIOS() {
    when(() => mockDevicePlatform.isAndroid()).thenReturn(false);
    when(() => mockDevicePlatform.isIOS()).thenReturn(true);
  }

  void mockDeviceIsOtherPlatform() {
    when(() => mockDevicePlatform.isAndroid()).thenReturn(false);
    when(() => mockDevicePlatform.isIOS()).thenReturn(false);
  }

  group('verify initDeviceInfo()', () {
    test('initDeviceInfo should initialize androidInfo for Android platform', () async {
      mockDeviceIsAndroid();

      await deviceInfoPluginWrapper.initDeviceInfo();

      expect(deviceInfoPluginWrapper.androidInfo, androidDeviceInfo30);
      expect(deviceInfoPluginWrapper.iosInfo, null);
    });

    test('initDeviceInfo should initialize iOSInfo for iOS platform', () async {
      mockDeviceIsIOS();

      await deviceInfoPluginWrapper.initDeviceInfo();

      expect(deviceInfoPluginWrapper.androidInfo, null);
      expect(deviceInfoPluginWrapper.iosInfo, iosInfo);
    });

    test('initDeviceInfo should return exception with others platform', () async {
      mockDeviceIsOtherPlatform();

      expect(deviceInfoPluginWrapper.initDeviceInfo(), throwsA(isA<Exception>()));
    });
  });

  group('verify getPlatformName()', () {
    test('getPlatformName should return "android" for Android platform', () {
      mockDeviceIsAndroid();

      final String platformName = deviceInfoPluginWrapper.getPlatformName();

      expect(platformName, 'android');
    });

    test('getPlatformName should return "ios" for iOS platform', () {
      mockDeviceIsIOS();

      final String platformName = deviceInfoPluginWrapper.getPlatformName();

      expect(platformName, 'ios');
    });

    test('getPlatformName should throw exception for other platform', () {
      mockDeviceIsOtherPlatform();

      expect(() => deviceInfoPluginWrapper.getPlatformName(), throwsA(isA<Exception>()));
    });
  });

  group('verify getDeviceModel(), getOSVersion(), getAndroidBuildNumber()', () {
    test('verify on Android with androidInfo is not null', () {
      mockDeviceIsAndroid();

      deviceInfoPluginWrapper.androidInfo = androidDeviceInfo30;

      final String? deviceModel = deviceInfoPluginWrapper.getDeviceModel();
      final String? deviceOsVersion = deviceInfoPluginWrapper.getOSVersion();
      final String? deviceAndroidBuildNumber = deviceInfoPluginWrapper.getAndroidBuildNumber();

      expect(deviceModel, '.manufacturer .model');
      expect(deviceOsVersion, '30');
      expect(deviceAndroidBuildNumber, '.id');
    });

    test('verify on Android with androidInfo is null', () {
      mockDeviceIsAndroid();

      deviceInfoPluginWrapper.androidInfo = null;

      final String? deviceModel = deviceInfoPluginWrapper.getDeviceModel();
      final String? deviceOsVersion = deviceInfoPluginWrapper.getOSVersion();
      final String? deviceAndroidBuildNumber = deviceInfoPluginWrapper.getAndroidBuildNumber();

      expect(deviceModel, null);
      expect(deviceOsVersion, null);
      expect(deviceAndroidBuildNumber, null);
    });

    test('verify on iOS with iosInfo is not null', () {
      deviceInfoPluginWrapper.iosInfo = iosInfo;

      mockDeviceIsIOS();

      final String? deviceModel = deviceInfoPluginWrapper.getDeviceModel();
      final String? deviceOsVersion = deviceInfoPluginWrapper.getOSVersion();
      final String? deviceAndroidBuildNumber = deviceInfoPluginWrapper.getAndroidBuildNumber();

      expect(deviceModel, 'machine');
      expect(deviceOsVersion, 'systemVersion');
      expect(deviceAndroidBuildNumber, null);
    });

    test('verify on iOS with iosInfo is null', () {
      deviceInfoPluginWrapper.iosInfo = null;

      mockDeviceIsIOS();

      final String? deviceModel = deviceInfoPluginWrapper.getDeviceModel();
      final String? deviceOsVersion = deviceInfoPluginWrapper.getOSVersion();
      final String? deviceAndroidBuildNumber = deviceInfoPluginWrapper.getAndroidBuildNumber();

      expect(deviceModel, null);
      expect(deviceOsVersion, null);
      expect(deviceAndroidBuildNumber, null);
    });
  });
}
