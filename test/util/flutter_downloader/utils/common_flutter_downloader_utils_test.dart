import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/flutter_downloader/utils/common_flutter_downloader_utils.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/util/path_provider_wrapper/android_path_provider_wrapper.dart';
import 'package:flutter_common_package/util/path_provider_wrapper/path_provider_wrapper.dart';
import 'package:flutter_common_package/util/permission/device_permission.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:permission_handler/permission_handler.dart';

class MockPathProviderWrapper extends Mock implements PathProviderWrapper {}

class MockAndroidPathProviderWrapper extends Mock implements AndroidPathProviderWrapper {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

class MockCommonFlutterDownloaderUtils extends CommonFlutterDownloaderUtils {
  MockCommonFlutterDownloaderUtils({
    required super.pathProviderWrapper,
    required super.androidPathProviderWrapper,
    required super.devicePlatform,
  });

  PermissionStatus permissionStatus = PermissionStatus.denied;

  @override
  Future<PermissionStatus> checkAndRequestPermission(TsDevicePermission devicePermission) async {
    return Future<PermissionStatus>.value(permissionStatus);
  }
}

class MockDirectory extends Mock implements Directory {
  @override
  final String path;

  @override
  Directory get absolute => path == '.' ? this : MockDirectory(path: path);

  MockDirectory({required this.path});
}

void main() {
  late CommonFlutterDownloaderUtils commonFlutterDownloaderUtils;
  final PathProviderWrapper mockPathProviderWrapper = MockPathProviderWrapper();
  final AndroidPathProviderWrapper mockAndroidPathProviderWrapper =
      MockAndroidPathProviderWrapper();
  final DevicePlatform mockDevicePlatform = MockDevicePlatform();
  late CommonUtilFunction mockCommonUtilFunction;

  const String fakeTemporaryDirectory = 'temp';
  const String fakeAppSupportDirectory = 'app_support';
  const String fakeLibraryDirectory = 'library';
  const String fakeAppDocsDirectory = 'app_docs';
  const String fakeDownloadDirectory = 'downloads';
  const String fakeExternalStorageDirectory = 'external_storage';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<CommonUtilFunction>(() => MockCommonUtilFunction());
    mockCommonUtilFunction = getIt.get<CommonUtilFunction>();
  });

  setUp(() {
    commonFlutterDownloaderUtils = CommonFlutterDownloaderUtils(
      pathProviderWrapper: mockPathProviderWrapper,
      androidPathProviderWrapper: mockAndroidPathProviderWrapper,
      devicePlatform: mockDevicePlatform,
    );

    when(() => mockPathProviderWrapper.getTemporaryDirectory())
        .thenAnswer((_) async => Directory(fakeTemporaryDirectory));
    when(() => mockPathProviderWrapper.getApplicationSupportDirectory())
        .thenAnswer((_) async => Directory(fakeAppSupportDirectory));
    when(() => mockPathProviderWrapper.getLibraryDirectory())
        .thenAnswer((_) async => Directory(fakeLibraryDirectory));
    when(() => mockPathProviderWrapper.getApplicationDocumentsDirectory())
        .thenAnswer((_) async => Directory(fakeAppDocsDirectory));
    when(() => mockPathProviderWrapper.getDownloadsDirectory())
        .thenAnswer((_) async => Directory(fakeDownloadDirectory));
    when(() => mockPathProviderWrapper.getExternalStorageDirectory())
        .thenAnswer((_) async => Directory(fakeExternalStorageDirectory));

    when(() => mockAndroidPathProviderWrapper.downloadsPath)
        .thenAnswer((_) async => Future<String>.value(fakeDownloadDirectory));

    when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
  });

  tearDown(() {
    reset(mockPathProviderWrapper);
    reset(mockAndroidPathProviderWrapper);
    reset(mockDevicePlatform);
    reset(mockCommonUtilFunction);
  });

  tearDownAll(() {
    getIt.reset();
  });

  test('verify constants', () {
    expect(commonFlutterDownloaderUtils.android10SdkVersion, 30);
    expect(commonFlutterDownloaderUtils.downloadFileNameDateFormat, 'yyyyMMddHHmmss');
  });

  group('verify requestStoragePermission()', () {
    late MockCommonFlutterDownloaderUtils mockCommonFlutterDownloaderUtils;

    setUp(() {
      mockCommonFlutterDownloaderUtils = MockCommonFlutterDownloaderUtils(
        pathProviderWrapper: mockPathProviderWrapper,
        androidPathProviderWrapper: mockAndroidPathProviderWrapper,
        devicePlatform: mockDevicePlatform,
      );
    });

    test('returns true for granted iOS storage permission', () async {
      when(() => mockDevicePlatform.isIOS()).thenReturn(true);

      mockCommonFlutterDownloaderUtils.permissionStatus = PermissionStatus.granted;

      final bool hasPermission = await mockCommonFlutterDownloaderUtils.requestStoragePermission();

      expect(hasPermission, true);
    });

    test('returns true for granted Android storage permission (SDK < 30)', () async {
      when(() => mockDevicePlatform.isIOS()).thenReturn(false);
      final AndroidDeviceInfo androidDeviceInfo29 = AndroidDeviceInfo.fromMap(<String, dynamic>{
        'version': <String, dynamic>{
          'sdkInt': 29,
          'baseOS': 'baseOS',
          'codename': 'codename',
          'incremental': 'incremental',
          'previewSdkInt': 30,
          'release': 'release',
          'securityPatch': 'securityPatch',
        },
        'board': '.board',
        'bootloader': '.bootloader',
        'brand': '.brand',
        'device': '.device',
        'display': '.display',
        'fingerprint': '.fingerprint',
        'hardware': '.hardware',
        'host': '.host',
        'id': '.id',
        'manufacturer': '.manufacturer',
        'model': '.model',
        'product': '.product',
        'supported32BitAbis': <String>[],
        'supported64BitAbis': <String>[],
        'supportedAbis': <String>[],
        'tags': '.tags',
        'type': '.type',
        'isPhysicalDevice': false,
        'systemFeatures': <String>[],
        'serialNumber': 'serialNumber',
        'isLowRamDevice': false,
      });

      when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
      when(() => mockCommonUtilFunction.getAndroidInfo())
          .thenAnswer((_) async => androidDeviceInfo29);

      mockCommonFlutterDownloaderUtils.permissionStatus = PermissionStatus.granted;

      final bool hasPermission = await mockCommonFlutterDownloaderUtils.requestStoragePermission();

      expect(hasPermission, true);
    });

    test('returns true for granted Android storage permission (SDK >= 30)', () async {
      when(() => mockDevicePlatform.isIOS()).thenReturn(false);
      final AndroidDeviceInfo androidDeviceInfo30 = AndroidDeviceInfo.fromMap(<String, dynamic>{
        'version': <String, dynamic>{
          'sdkInt': 30,
          'baseOS': 'baseOS',
          'codename': 'codename',
          'incremental': 'incremental',
          'previewSdkInt': 30,
          'release': 'release',
          'securityPatch': 'securityPatch',
        },
        'board': '.board',
        'bootloader': '.bootloader',
        'brand': '.brand',
        'device': '.device',
        'display': '.display',
        'fingerprint': '.fingerprint',
        'hardware': '.hardware',
        'host': '.host',
        'id': '.id',
        'manufacturer': '.manufacturer',
        'model': '.model',
        'product': '.product',
        'supported32BitAbis': <String>[],
        'supported64BitAbis': <String>[],
        'supportedAbis': <String>[],
        'tags': '.tags',
        'type': '.type',
        'isPhysicalDevice': false,
        'systemFeatures': <String>[],
        'serialNumber': 'serialNumber',
        'isLowRamDevice': false,
      });

      when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
      when(() => mockCommonUtilFunction.getAndroidInfo())
          .thenAnswer((_) async => androidDeviceInfo30);

      final bool hasPermission = await commonFlutterDownloaderUtils.requestStoragePermission();

      expect(hasPermission, true);
    });

    test('throws UnsupportedError for unsupported platform', () {
      when(() => mockDevicePlatform.isIOS()).thenReturn(false);
      when(() => mockDevicePlatform.isAndroid()).thenReturn(false);

      expect(
        () => commonFlutterDownloaderUtils.requestStoragePermission(),
        throwsA(isA<UnsupportedError>()),
      );
    });
  });

  group('verify IOSFolderType', () {
    test('should have correct integer values for each enum value', () {
      expect(IOSFolderType.temporary.value, 0);
      expect(IOSFolderType.applicationSupport.value, 1);
      expect(IOSFolderType.library.value, 2);
      expect(IOSFolderType.applicationDocuments.value, 3);
      expect(IOSFolderType.downloads.value, 4);
    });
  });

  group('verify formatFileDownload()', () {
    const String fakeFileName = 'fake_file_name';

    test('verify suggestedFilename is null', () {
      final String? result = commonFlutterDownloaderUtils.formatFileDownload(null);
      expect(result, isNull);
    });

    test('verify suggestedFilename is empty', () {
      final String? result = commonFlutterDownloaderUtils.formatFileDownload('');
      expect(result, isNull);
    });

    test('verify suggestedFilename has not extension', () {
      final String? result = commonFlutterDownloaderUtils.formatFileDownload(fakeFileName);
      expect(result, fakeFileName);
    });

    test('verify suggestedFilename has extension', () {
      const String extensionFile = '.pdf';
      const String fileName = '$fakeFileName$extensionFile';

      final String? result = commonFlutterDownloaderUtils.formatFileDownload(fileName);
      expect(result?.contains(fakeFileName), true);
      expect(result?.contains(extensionFile), true);
    });
  });

  group('verify createFileNameWithTimeStamp()', () {
    const String fakeFileName = 'fake_file_name';
    const String fakeExtension = '.pdf';
    final DateTime timeStamp = DateTime(2021, 08, 10, 10, 11, 12);

    test('verify createFileNameWithTimeStamp to return right value', () {
      final String result = commonFlutterDownloaderUtils.createFileNameWithTimeStamp(
        fileName: fakeFileName,
        extension: fakeExtension,
        timeStamp: timeStamp,
      );

      const String expectedFileName = 'fake_file_name_20210810101112.pdf';
      expect(result, expectedFileName);
    });

    test('verify createFileNameWithTimeStamp with filename is empty', () {
      final String result = commonFlutterDownloaderUtils.createFileNameWithTimeStamp(
        fileName: '',
        extension: fakeExtension,
        timeStamp: timeStamp,
      );

      const String expectedFileName = '_20210810101112.pdf';
      expect(result, expectedFileName);
    });

    test('verify createFileNameWithTimeStamp with fakeExtension is empty', () {
      final String result = commonFlutterDownloaderUtils.createFileNameWithTimeStamp(
        fileName: fakeFileName,
        extension: '',
        timeStamp: timeStamp,
      );

      const String expectedFileName = 'fake_file_name_20210810101112';
      expect(result, expectedFileName);
    });
  });

  group('verify getSavedDir()', () {
    test('returns downloadsPath for Android', () async {
      final String? savedDir = await commonFlutterDownloaderUtils.getSavedDir();

      expect(savedDir, fakeDownloadDirectory);

      verify(() => mockAndroidPathProviderWrapper.downloadsPath).called(1);
    });

    test('returns application documents directory for iOS', () async {
      when(() => mockDevicePlatform.isAndroid()).thenReturn(false);
      when(() => mockDevicePlatform.isIOS()).thenReturn(true);

      when(() => mockPathProviderWrapper.getApplicationDocumentsDirectory())
          .thenAnswer((_) async => MockDirectory(path: fakeAppDocsDirectory));

      final String? result = await commonFlutterDownloaderUtils.getSavedDir();
      expect(result, fakeAppDocsDirectory);

      verify(() => mockPathProviderWrapper.getApplicationDocumentsDirectory()).called(1);
    });

    test('returns external storage directory for Android when downloadsPath fails', () async {
      when(() => mockAndroidPathProviderWrapper.downloadsPath)
          .thenThrow(Exception('Failed to get downloads path'));

      final String? savedDir = await commonFlutterDownloaderUtils.getSavedDir();

      expect(savedDir, fakeExternalStorageDirectory);

      verify(() => mockPathProviderWrapper.getExternalStorageDirectory()).called(1);
    });
  });

  group('verify DirsOnIOS', () {
    test('returns a list of directories', () async {
      final List<Directory?> dirs = await commonFlutterDownloaderUtils.dirsOnIOS;

      expect(dirs.length, 5);
      expect(dirs[0]?.path, fakeTemporaryDirectory);
      expect(dirs[1]?.path, fakeAppSupportDirectory);
      expect(dirs[2]?.path, fakeLibraryDirectory);
      expect(dirs[3]?.path, fakeAppDocsDirectory);
      expect(dirs[4]?.path, fakeDownloadDirectory);
    });
  });
}
