// ignore_for_file: avoid_redundant_argument_values

import 'package:dio/dio.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/common_request_option.dart';
import 'package:flutter_common_package/data/http_client/dio_http_client_impl.dart';
import 'package:flutter_common_package/data/http_client/dio_request_option_mapper.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockDio extends Mock implements Dio {}

void main() {
  late CommonHttpClient commonHttpClient;

  final Dio dio = Dio();

  const String sessionTokenKey = 'sessionTokenKey';
  const String userIdKey = 'userIdKey';

  const String sessionToken = 'sessionToken';
  const String userId = 'userId';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    dio.options = BaseOptions();
    dio.options.baseUrl = 'https://www.google.com';
    commonHttpClient = DioClientImpl(
      dio,
      dioRequestOptionMapper: DioRequestOptionMapper(),
    );

    getIt.registerLazySingleton(() => CommonUtilFunction());
  });

  tearDownAll(() {
    getIt.reset();
  });

  test('test static values', () {
    expect(CommonHttpClient.SUCCESS, 200);
    expect(CommonHttpClient.CREATED, 201);

    expect(CommonHttpClient.NO_INTERNET, 12029);
    expect(CommonHttpClient.SOCKET_ERRORS, 7);
    expect(CommonHttpClient.INVALID_FORMAT, 622);
    expect(CommonHttpClient.CONNECTION_TIMEOUT, 608);
    expect(CommonHttpClient.UNKNOWN_ERRORS, 600);

    expect(CommonHttpClient.BAD_REQUEST, 400);
    expect(CommonHttpClient.INVALID_TOKEN, 401);
    expect(CommonHttpClient.NOT_FOUND, 404);
    expect(CommonHttpClient.DUPLICATE, 409);
    expect(CommonHttpClient.LIMIT_EXCEEDED, 429);
    expect(CommonHttpClient.REQUEST_TIMEOUT, 408);
    expect(CommonHttpClient.LOCKED_RESOURCE, 423);
    expect(CommonHttpClient.INTERNAL_SERVER_ERROR, 500);

    expect(CommonHttpClient.xRequestIdHeader, 'x-request-id');
  });

  group('test set and get base url', () {
    test('Give base url, then set base url success', () {
      const String baseUrl = 'https://www.google.com/';
      commonHttpClient.setBaseUrl(baseUrl);
      expect(commonHttpClient.getBaseUrl(), baseUrl);

      const String newBaseUrl = 'https://www.another_url.com/';
      commonHttpClient.setBaseUrl(newBaseUrl);
      expect(commonHttpClient.getBaseUrl(), newBaseUrl);
    });

    tearDownAll(() {
      commonHttpClient.setBaseUrl('');
    });
  });

  group('test addHeaders', () {
    tearDown(() {
      commonHttpClient.getHeaders().clear();
    });

    test('test addHeaders and get correct value', () {
      final Map<String, dynamic> headers = commonHttpClient.getHeaders();
      expect(headers[sessionTokenKey], isNull);
      expect(headers[userIdKey], isNull);

      commonHttpClient.addHeaders(<String, String>{
        sessionTokenKey: sessionToken,
        userIdKey: userId,
      });

      final Map<String, dynamic> headersAfterAdd = commonHttpClient.getHeaders();
      expect(headersAfterAdd[sessionTokenKey], sessionToken);
      expect(headersAfterAdd[userIdKey], userId);
    });

    test('test addHeaders and get incorrect value with non-existed key', () {
      final Map<String, dynamic> headers = commonHttpClient.getHeaders();
      expect(headers[sessionTokenKey], isNull);
      expect(headers[userIdKey], isNull);

      commonHttpClient.addHeaders(<String, String>{
        sessionTokenKey: sessionToken,
      });

      final Map<String, dynamic> headersAfterAdd = commonHttpClient.getHeaders();
      expect(headersAfterAdd['incorrectKey'], isNull);
      expect(headersAfterAdd.containsKey('incorrectKey'), isFalse);
    });
  });

  group('test getHeaders', () {
    tearDown(() {
      commonHttpClient.getHeaders().clear();
    });

    test('test getHeaders isEmpty', () {
      final Map<String, dynamic> headers = commonHttpClient.getHeaders();
      expect(headers.isEmpty, isTrue);
    });

    test('test getHeaders isNotEmpty after add header', () {
      dio.options.headers.addAll(<String, String>{
        sessionTokenKey: sessionToken,
        userIdKey: userId,
      });

      final Map<String, dynamic> headers = commonHttpClient.getHeaders();
      expect(headers.isNotEmpty, isTrue);
      expect(headers.containsKey(sessionTokenKey), isTrue);
      expect(headers.containsKey(userIdKey), isTrue);
    });
  });

  group('test removeHeader', () {
    test('Give existed "key", then remove this "key" success', () {
      commonHttpClient.addHeaders(<String, String?>{'key': 'value'});
      expect(commonHttpClient.getHeaders().containsKey('key'), true);

      commonHttpClient.removeHeader('key');
      expect(commonHttpClient.getHeaders().containsKey('key'), false);
    });

    test('Give non-existed "key", then call removeHeader without error', () {
      commonHttpClient.removeHeader('key');
      expect(() => commonHttpClient.removeHeader('key'), returnsNormally);
    });

    tearDownAll(() {
      commonHttpClient.getHeaders().clear();
    });
  });

  group('test GET method', () {
    test('test CommonHttpClient call get() function and get correct method name', () async {
      final BaseResponse response = await commonHttpClient.get('apiUrl');

      expect(response.requestMethod, 'GET');
    });

    test('should throw a exception when mock is ENABLE but FILE NAME of mock is null', () async {
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: null,
      );

      expect(() => commonHttpClient.get('url', mockConfig: mockConfig), throwsAssertionError);
    });

    test('should response exact value when mock is ENABLE and response 200', () async {
      const String fileName = 'test_mock_200.json';
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: fileName,
        statusCode: 200,
      );

      final Map<String, dynamic> expectedMap = <String, dynamic>{
        'message': '',
        'time': '2022-10-26T02:35:54Z',
        'verdict': 'success',
        'user_message': '',
        'data': <String, dynamic>{'id': 1, 'title': 'test_title'}
      };

      final BaseResponse actualResponse = await commonHttpClient.get('url', mockConfig: mockConfig);

      expect(actualResponse.response, expectedMap);
      expect(actualResponse.statusCode, 200);
    });

    test('should response exact value when mock is ENABLE and response 400', () async {
      const String fileName = 'test_mock_400.json';
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: fileName,
        statusCode: 400,
      );

      final Map<String, dynamic> expectedMap = <String, dynamic>{
        'message': 'bad_request',
        'time': '2022-10-26T02:35:54Z',
        'verdict': 'bad_request',
        'data': <String, dynamic>{'user_message': 'some message'},
        'status_code': 400
      };

      final BaseResponse actualResponse = await commonHttpClient.get('url', mockConfig: mockConfig);

      expect(actualResponse.response, expectedMap);
      expect(actualResponse.statusCode, 400);
    });
  });

  group('test POST method', () {
    test('test CommonHttpClient call post() function and get correct method name', () async {
      final BaseResponse response = await commonHttpClient.post('apiUrl');

      expect(response.requestMethod, 'POST');
    });

    test('should throw a exception when mock is ENABLE but FILE NAME of mock is null', () async {
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: null,
      );

      expect(() => commonHttpClient.post('url', mockConfig: mockConfig), throwsAssertionError);
    });

    test('should response exact value when mock is ENABLE and response 200', () async {
      const String fileName = 'test_mock_200.json';
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: fileName,
        statusCode: 200,
      );

      final Map<String, dynamic> expectedMap = <String, dynamic>{
        'message': '',
        'time': '2022-10-26T02:35:54Z',
        'verdict': 'success',
        'user_message': '',
        'data': <String, dynamic>{'id': 1, 'title': 'test_title'}
      };

      final BaseResponse actualResponse =
          await commonHttpClient.post('url', mockConfig: mockConfig);

      expect(actualResponse.response, expectedMap);
      expect(actualResponse.statusCode, 200);
    });

    test('should response exact value when mock is ENABLE and response 400', () async {
      const String fileName = 'test_mock_400.json';
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: fileName,
        statusCode: 400,
      );

      final Map<String, dynamic> expectedMap = <String, dynamic>{
        'message': 'bad_request',
        'time': '2022-10-26T02:35:54Z',
        'verdict': 'bad_request',
        'data': <String, dynamic>{'user_message': 'some message'},
        'status_code': 400
      };

      final BaseResponse actualResponse =
          await commonHttpClient.post('url', mockConfig: mockConfig);

      expect(actualResponse.response, expectedMap);
      expect(actualResponse.statusCode, 400);
    });
  });

  group('test PUT method', () {
    test('test CommonHttpClient call put() function and get correct method name', () async {
      final BaseResponse response = await commonHttpClient.put('apiUrl');

      expect(response.requestMethod, 'PUT');
    });

    test('should throw a exception when mock is ENABLE but FILE NAME of mock is null', () async {
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: null,
      );

      expect(() => commonHttpClient.put('url', mockConfig: mockConfig), throwsAssertionError);
    });

    test('should response exact value when mock is ENABLE and response 200', () async {
      const String fileName = 'test_mock_200.json';
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: fileName,
        statusCode: 200,
      );

      final Map<String, dynamic> expectedMap = <String, dynamic>{
        'message': '',
        'time': '2022-10-26T02:35:54Z',
        'verdict': 'success',
        'user_message': '',
        'data': <String, dynamic>{'id': 1, 'title': 'test_title'}
      };

      final BaseResponse actualResponse = await commonHttpClient.put('url', mockConfig: mockConfig);

      expect(actualResponse.response, expectedMap);
      expect(actualResponse.statusCode, 200);
    });

    test('should response exact value when mock is ENABLE and response 400', () async {
      const String fileName = 'test_mock_400.json';
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: fileName,
        statusCode: 400,
      );

      final Map<String, dynamic> expectedMap = <String, dynamic>{
        'message': 'bad_request',
        'time': '2022-10-26T02:35:54Z',
        'verdict': 'bad_request',
        'data': <String, dynamic>{'user_message': 'some message'},
        'status_code': 400
      };

      final BaseResponse actualResponse = await commonHttpClient.put('url', mockConfig: mockConfig);

      expect(actualResponse.response, expectedMap);
      expect(actualResponse.statusCode, 400);
    });
  });

  group('test PATCH method', () {
    test('test CommonHttpClient call patch() function and get correct method name', () async {
      final BaseResponse response = await commonHttpClient.patch('apiUrl');

      expect(response.requestMethod, 'PATCH');
    });

    test('should throw a exception when mock is ENABLE but FILE NAME of mock is null', () async {
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: null,
      );

      expect(() => commonHttpClient.patch('url', mockConfig: mockConfig), throwsAssertionError);
    });

    test('should response exact value when mock is ENABLE and response 200', () async {
      const String fileName = 'test_mock_200.json';
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: fileName,
        statusCode: 200,
      );

      final Map<String, dynamic> expectedMap = <String, dynamic>{
        'message': '',
        'time': '2022-10-26T02:35:54Z',
        'verdict': 'success',
        'user_message': '',
        'data': <String, dynamic>{'id': 1, 'title': 'test_title'}
      };

      final BaseResponse actualResponse =
          await commonHttpClient.patch('url', mockConfig: mockConfig);

      expect(actualResponse.response, expectedMap);
      expect(actualResponse.statusCode, 200);
    });

    test('should response exact value when mock is ENABLE and response 400', () async {
      const String fileName = 'test_mock_400.json';
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: fileName,
        statusCode: 400,
      );

      final Map<String, dynamic> expectedMap = <String, dynamic>{
        'message': 'bad_request',
        'time': '2022-10-26T02:35:54Z',
        'verdict': 'bad_request',
        'data': <String, dynamic>{'user_message': 'some message'},
        'status_code': 400
      };

      final BaseResponse actualResponse =
          await commonHttpClient.patch('url', mockConfig: mockConfig);

      expect(actualResponse.response, expectedMap);
      expect(actualResponse.statusCode, 400);
    });
  });

  group('test DELETE method', () {
    test('test CommonHttpClient call delete() function and get correct method name', () async {
      final BaseResponse response = await commonHttpClient.delete('apiUrl');

      expect(response.requestMethod, 'DELETE');
    });

    test('should throw a exception when mock is ENABLE but FILE NAME of mock is null', () async {
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: null,
      );

      expect(() => commonHttpClient.delete('url', mockConfig: mockConfig), throwsAssertionError);
    });

    test('should response exact value when mock is ENABLE and response 200', () async {
      const String fileName = 'test_mock_200.json';
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: fileName,
        statusCode: 200,
      );

      final Map<String, dynamic> expectedMap = <String, dynamic>{
        'message': '',
        'time': '2022-10-26T02:35:54Z',
        'verdict': 'success',
        'user_message': '',
        'data': <String, dynamic>{'id': 1, 'title': 'test_title'}
      };

      final BaseResponse actualResponse =
          await commonHttpClient.delete('url', mockConfig: mockConfig);

      expect(actualResponse.response, expectedMap);
      expect(actualResponse.statusCode, 200);
    });

    test('should response exact value when mock is ENABLE and response 400', () async {
      const String fileName = 'test_mock_400.json';
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: fileName,
        statusCode: 400,
      );

      final Map<String, dynamic> expectedMap = <String, dynamic>{
        'message': 'bad_request',
        'time': '2022-10-26T02:35:54Z',
        'verdict': 'bad_request',
        'data': <String, dynamic>{'user_message': 'some message'},
        'status_code': 400
      };

      final BaseResponse actualResponse =
          await commonHttpClient.delete('url', mockConfig: mockConfig);

      expect(actualResponse.response, expectedMap);
      expect(actualResponse.statusCode, 400);
    });
  });

  group('test custom Option for each dio request', () {
    const String url = 'url';

    const String dioKey = 'dioKey';
    const String dioValue = 'dioValue';
    const String requestKey1 = 'requestKey1';
    const String requestValue1 = 'requestValue1';
    const String requestValue2 = 'requestValue2';
    const Duration receiveTimeout = Duration(seconds: 15);
    const Duration defaultReceiveTimeout = Duration(seconds: 10);

    late CommonRequestOption option;

    setUp(() {
      dio.options.headers.clear();
      dio.options.headers.addAll(<String, dynamic>{dioKey: dioValue});
      dio.options.receiveTimeout = defaultReceiveTimeout;

      option = CommonRequestOption(
        headers: <String, dynamic>{
          requestKey1: requestValue1,
          dioKey: requestValue2,
        },
        receiveTimeout: receiveTimeout,
      );
    });

    test('test for GET method', () async {
      final BaseResponse response = await commonHttpClient.get(
        url,
        requestOption: option,
      );

      /// Make sure header of request is append new key.
      expect(response.requestHeaders?[requestKey1], requestValue1);

      /// Make sure header of request will override dio header value.
      expect(response.requestHeaders?[dioKey], requestValue2);

      /// Make sure request receiveTimeout is correct.
      expect(response.requestOptions?.receiveTimeout?.inSeconds, receiveTimeout.inSeconds);

      /// Make sure header of dio is not change after request complete.
      expect(dio.options.headers[dioKey], dioValue);

      /// Make sure header of dio is not append new key from request after request complete.
      expect(dio.options.headers[requestKey1], isNull);

      /// Make sure request receiveTimeout is not change after request complete.
      expect(dio.options.receiveTimeout?.inSeconds, defaultReceiveTimeout.inSeconds);
    });

    test('test for POST method', () async {
      final BaseResponse response = await commonHttpClient.post(
        url,
        requestOption: option,
      );

      /// Make sure header of request is append new key.
      expect(response.requestHeaders?[requestKey1], requestValue1);

      /// Make sure header of request will override dio header value.
      expect(response.requestHeaders?[dioKey], requestValue2);

      /// Make sure request receiveTimeout is correct.
      expect(response.requestOptions?.receiveTimeout?.inSeconds, receiveTimeout.inSeconds);

      /// Make sure header of dio is not change after request complete.
      expect(dio.options.headers[dioKey], dioValue);

      /// Make sure header of dio is not append new key from request after request complete.
      expect(dio.options.headers[requestKey1], isNull);

      /// Make sure request receiveTimeout is not change after request complete.
      expect(dio.options.receiveTimeout?.inSeconds, defaultReceiveTimeout.inSeconds);
    });

    test('test for PUT method', () async {
      final BaseResponse response = await commonHttpClient.put(
        url,
        requestOption: option,
      );

      /// Make sure header of request is append new key.
      expect(response.requestHeaders?[requestKey1], requestValue1);

      /// Make sure header of request will override dio header value.
      expect(response.requestHeaders?[dioKey], requestValue2);

      /// Make sure request receiveTimeout is correct.
      expect(response.requestOptions?.receiveTimeout?.inSeconds, receiveTimeout.inSeconds);

      /// Make sure header of dio is not change after request complete.
      expect(dio.options.headers[dioKey], dioValue);

      /// Make sure header of dio is not append new key from request after request complete.
      expect(dio.options.headers[requestKey1], isNull);

      /// Make sure request receiveTimeout is not change after request complete.
      expect(dio.options.receiveTimeout?.inSeconds, defaultReceiveTimeout.inSeconds);
    });

    test('test for PATCH method', () async {
      final BaseResponse response = await commonHttpClient.patch(
        url,
        requestOption: option,
      );

      /// Make sure header of request is append new key.
      expect(response.requestHeaders?[requestKey1], requestValue1);

      /// Make sure header of request will override dio header value.
      expect(response.requestHeaders?[dioKey], requestValue2);

      /// Make sure request receiveTimeout is correct.
      expect(response.requestOptions?.receiveTimeout?.inSeconds, receiveTimeout.inSeconds);

      /// Make sure header of dio is not change after request complete.
      expect(dio.options.headers[dioKey], dioValue);

      /// Make sure header of dio is not append new key from request after request complete.
      expect(dio.options.headers[requestKey1], isNull);

      /// Make sure request receiveTimeout is not change after request complete.
      expect(dio.options.receiveTimeout?.inSeconds, defaultReceiveTimeout.inSeconds);
    });

    test('test for DELETE method', () async {
      final BaseResponse response = await commonHttpClient.delete(
        url,
        requestOption: option,
      );

      /// Make sure header of request is append new key.
      expect(response.requestHeaders?[requestKey1], requestValue1);

      /// Make sure header of request will override dio header value.
      expect(response.requestHeaders?[dioKey], requestValue2);

      /// Make sure request receiveTimeout is correct.
      expect(response.requestOptions?.receiveTimeout?.inSeconds, receiveTimeout.inSeconds);

      /// Make sure header of dio is not change after request complete.
      expect(dio.options.headers[dioKey], dioValue);

      /// Make sure header of dio is not append new key from request after request complete.
      expect(dio.options.headers[requestKey1], isNull);

      /// Make sure request receiveTimeout is not change after request complete.
      expect(dio.options.receiveTimeout?.inSeconds, defaultReceiveTimeout.inSeconds);
    });
  });

  group('Test DOWNLOAD method', () {
    const String downloadUrl = 'url';
    const String savePath = 'savePath';

    test('test CommonHttpClient call download() function, method should be GET', () async {
      final BaseResponse response = await commonHttpClient.download(downloadUrl, savePath);

      expect(response.requestMethod, 'GET');
    });

    test('should throw a exception when mock is ENABLE but FILE NAME of mock is null', () async {
      const MockConfig mockConfig = MockConfig(
        enable: true,
        fileName: null,
      );

      expect(
        () => commonHttpClient.download(downloadUrl, savePath, mockConfig: mockConfig),
        throwsAssertionError,
      );
    });

    test(
        'Give null CommonRequestOption, should call download with CommonRequestOption with header should_remove_access_token',
        () {
      final Dio mockDio = MockDio();
      commonHttpClient = DioClientImpl(
        mockDio,
        dioRequestOptionMapper: DioRequestOptionMapper(),
      );

      when(
        () => mockDio.download(
          downloadUrl,
          savePath,
          options: any(named: 'options'),
          queryParameters: any(named: 'queryParameters'),
          data: any(named: 'data'),
          onReceiveProgress: any(named: 'onReceiveProgress'),
        ),
      ).thenAnswer((Invocation invocation) async {
        return Response<dynamic>(
          requestOptions: RequestOptions(path: downloadUrl),
          data: null,
          statusCode: CommonHttpClient.SUCCESS,
        );
      });

      final Map<String, dynamic> params = <String, dynamic>{'param-key': 'param-value'};
      final Map<String, dynamic> data = <String, dynamic>{'data-key': 'data-value'};
      onReceiveProgress(int count, int total) {}

      commonHttpClient.download(
        downloadUrl,
        savePath,
        params: params,
        data: data,
        onReceiveProgress: onReceiveProgress,
      );

      expect(
        verify(
          () => mockDio.download(
            downloadUrl,
            savePath,
            options: captureAny(named: 'options'),
            queryParameters: captureAny(named: 'queryParameters'),
            data: captureAny(named: 'data'),
            onReceiveProgress: captureAny(named: 'onReceiveProgress'),
          ),
        ).captured,
        <dynamic>[
          isA<Options>().having(
            (Options option) => option.headers?[CommonRequestOption.shouldRemoveAccessTokenHeader],
            'test request options',
            true,
          ),
          params,
          data,
          onReceiveProgress,
        ],
      );
    });

    test(
        'Give empty CommonRequestOption, should call download with CommonRequestOption with header should_remove_access_token',
        () {
      final Dio mockDio = MockDio();
      commonHttpClient = DioClientImpl(
        mockDio,
        dioRequestOptionMapper: DioRequestOptionMapper(),
      );

      when(
        () => mockDio.download(
          downloadUrl,
          savePath,
          options: any(named: 'options'),
          queryParameters: any(named: 'queryParameters'),
          data: any(named: 'data'),
          onReceiveProgress: any(named: 'onReceiveProgress'),
        ),
      ).thenAnswer((Invocation invocation) async {
        return Response<dynamic>(
          requestOptions: RequestOptions(path: downloadUrl),
          data: null,
          statusCode: CommonHttpClient.SUCCESS,
        );
      });

      final CommonRequestOption commonRequestOption = CommonRequestOption();
      final Map<String, dynamic> params = <String, dynamic>{'param-key': 'param-value'};
      final Map<String, dynamic> data = <String, dynamic>{'data-key': 'data-value'};
      onReceiveProgress(int count, int total) {}

      commonHttpClient.download(
        downloadUrl,
        savePath,
        requestOption: commonRequestOption,
        params: params,
        data: data,
        onReceiveProgress: onReceiveProgress,
      );

      expect(
        verify(
          () => mockDio.download(
            downloadUrl,
            savePath,
            options: captureAny(named: 'options'),
            queryParameters: captureAny(named: 'queryParameters'),
            data: captureAny(named: 'data'),
            onReceiveProgress: captureAny(named: 'onReceiveProgress'),
          ),
        ).captured,
        <dynamic>[
          isA<Options>().having(
            (Options option) => option.headers?[CommonRequestOption.shouldRemoveAccessTokenHeader],
            'test request options',
            true,
          ),
          params,
          data,
          onReceiveProgress,
        ],
      );
    });

    test(
        'Give with CommonRequestOption without header should_remove_access_token, should call download with CommonRequestOption with header should_remove_access_token',
        () {
      final Dio mockDio = MockDio();
      commonHttpClient = DioClientImpl(
        mockDio,
        dioRequestOptionMapper: DioRequestOptionMapper(),
      );

      when(
        () => mockDio.download(
          downloadUrl,
          savePath,
          options: any(named: 'options'),
          queryParameters: any(named: 'queryParameters'),
          data: any(named: 'data'),
          onReceiveProgress: any(named: 'onReceiveProgress'),
        ),
      ).thenAnswer((Invocation invocation) async {
        return Response<dynamic>(
          requestOptions: RequestOptions(path: downloadUrl),
          data: null,
          statusCode: CommonHttpClient.SUCCESS,
        );
      });

      const Duration receiveTimeout = Duration(seconds: 15);
      final CommonRequestOption commonRequestOption = CommonRequestOption(
        headers: <String, dynamic>{'header-key': 'header-value'},
        receiveTimeout: receiveTimeout,
      );
      final Map<String, dynamic> params = <String, dynamic>{'param-key': 'param-value'};
      final Map<String, dynamic> data = <String, dynamic>{'data-key': 'data-value'};
      onReceiveProgress(int count, int total) {}

      commonHttpClient.download(
        downloadUrl,
        savePath,
        requestOption: commonRequestOption,
        params: params,
        data: data,
        onReceiveProgress: onReceiveProgress,
      );

      expect(
        verify(
          () => mockDio.download(
            downloadUrl,
            savePath,
            options: captureAny(named: 'options'),
            queryParameters: captureAny(named: 'queryParameters'),
            data: captureAny(named: 'data'),
            onReceiveProgress: captureAny(named: 'onReceiveProgress'),
          ),
        ).captured,
        <dynamic>[
          isA<Options>().having(
            (Options option) =>
                option.receiveTimeout?.inSeconds == receiveTimeout.inSeconds &&
                option.headers?['header-key'] == 'header-value' &&
                option.headers?[CommonRequestOption.shouldRemoveAccessTokenHeader] == true,
            'test request options',
            true,
          ),
          params,
          data,
          onReceiveProgress,
        ],
      );
    });
  });

  group('Test function addHeaderToRemoveAccessToken', () {
    final DioClientImpl dioClientImpl = DioClientImpl(
      MockDio(),
      dioRequestOptionMapper: DioRequestOptionMapper(),
    );

    test(
        'Give null CommonRequestOption, should return CommonRequestOption with header should_remove_access_token',
        () {
      final CommonRequestOption result = dioClientImpl.addHeaderToRemoveAccessToken(null);
      expect(result.headers?[CommonRequestOption.shouldRemoveAccessTokenHeader], true);
    });

    test(
        'Give empty CommonRequestOption, should return CommonRequestOption with header should_remove_access_token',
        () {
      final CommonRequestOption result = dioClientImpl.addHeaderToRemoveAccessToken(
        CommonRequestOption(),
      );
      expect(result.headers?[CommonRequestOption.shouldRemoveAccessTokenHeader], true);
    });

    test(
        'Give CommonRequestOption without header should_remove_access_token, should return CommonRequestOption with header should_remove_access_token',
        () {
      final CommonRequestOption commonRequestOption = CommonRequestOption(
        headers: <String, dynamic>{'header-key': 'header-value'},
      );
      final CommonRequestOption result = dioClientImpl.addHeaderToRemoveAccessToken(
        commonRequestOption,
      );
      expect(result.headers?['header-key'], 'header-value');
      expect(result.headers?[CommonRequestOption.shouldRemoveAccessTokenHeader], true);
    });

    test(
        'Give CommonRequestOption with header should_remove_access_token = false, should return CommonRequestOption with header should_remove_access_token = true',
        () {
      final CommonRequestOption commonRequestOption = CommonRequestOption(
        headers: <String, dynamic>{
          'header-key': 'header-value',
          CommonRequestOption.shouldRemoveAccessTokenHeader: false,
        },
      );
      final CommonRequestOption result = dioClientImpl.addHeaderToRemoveAccessToken(
        commonRequestOption,
      );
      expect(result.headers?['header-key'], 'header-value');
      expect(result.headers?[CommonRequestOption.shouldRemoveAccessTokenHeader], true);
    });
  });
}
