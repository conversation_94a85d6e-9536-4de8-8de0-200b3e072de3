import 'package:flutter/services.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/data_collection/storage_info_wrapper/storage_info_wrapper_impl.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/feature/data_collection/model/storage_info.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockLoggingRepo extends Mock implements LoggingRepo {}

void main() {
  const String channelName = 'disk_space_plus';
  const String getFreeDiskSpaceMethodName = 'getFreeDiskSpace';
  const String getTotalDiskSpaceMethodName = 'getTotalDiskSpace';
  const MethodChannel channel = MethodChannel(channelName);

  const double expectedFreeSpace = 20;
  const double expectedTotalSpace = 64;

  late LoggingRepo mockLoggingRepo;
  late StorageInfoWrapperImpl storageSpaceWrapper;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    mockLoggingRepo = getIt.get<LoggingRepo>();

    storageSpaceWrapper = StorageInfoWrapperImpl();
  });

  tearDown(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      null,
    );
  });

  group('Test function getStorageInfo', () {
    test('Get storage info success', () async {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == getFreeDiskSpaceMethodName) {
            return expectedFreeSpace;
          }
          if (methodCall.method == getTotalDiskSpaceMethodName) {
            return expectedTotalSpace;
          }
          return null;
        },
      );

      final StorageInfo storageInfo = await storageSpaceWrapper.getStorageInfo();
      expect(
          storageInfo,
          isA<StorageInfo>()
              .having(
                (StorageInfo p0) => p0.freeStorageInMegabytes,
                'test freeStorageInMegabytes',
                expectedFreeSpace,
              )
              .having(
                (StorageInfo p0) => p0.totalStorageInMegabytes,
                'test totalStorageInMegabytes',
                expectedTotalSpace,
              ));
    });

    test('Get free storage info error', () async {
      when(() => mockLoggingRepo.logErrorEvent(
            errorType: any(named: 'errorType'),
            args: any(named: 'args'),
          )).thenAnswer((_) async => Future<void>.value());

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == getFreeDiskSpaceMethodName) {
            throw PlatformException(code: 'ERROR', message: 'Cannot get free space');
          }
          if (methodCall.method == getTotalDiskSpaceMethodName) {
            return expectedTotalSpace;
          }
          return null;
        },
      );

      final StorageInfo result = await storageSpaceWrapper.getStorageInfo();

      expect(
        verify(() => mockLoggingRepo.logErrorEvent(
              errorType: captureAny(named: 'errorType'),
              args: captureAny(named: 'args'),
            )).captured,
        <dynamic>[
          'storage_info_wrapper',
          <String, dynamic>{
            'action': 'get_storage_info',
            'description': 'Cannot get free space',
          }
        ],
      );
      expect(
          result,
          isA<StorageInfo>()
              .having(
                (StorageInfo p0) => p0.freeStorageInMegabytes,
                'test freeStorageInMegabytes',
                isNull,
              )
              .having(
                (StorageInfo p0) => p0.totalStorageInMegabytes,
                'test totalStorageInMegabytes',
                isNull,
              ));
    });

    test('Get total storage info error', () async {
      when(() => mockLoggingRepo.logErrorEvent(
            errorType: any(named: 'errorType'),
            args: any(named: 'args'),
          )).thenAnswer((_) async => Future<void>.value());

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          if (methodCall.method == getFreeDiskSpaceMethodName) {
            return expectedFreeSpace;
          }
          if (methodCall.method == getTotalDiskSpaceMethodName) {
            throw PlatformException(code: 'ERROR', message: 'Cannot get total space');
          }
          return null;
        },
      );

      final StorageInfo result = await storageSpaceWrapper.getStorageInfo();

      expect(
        verify(() => mockLoggingRepo.logErrorEvent(
              errorType: captureAny(named: 'errorType'),
              args: captureAny(named: 'args'),
            )).captured,
        <dynamic>[
          'storage_info_wrapper',
          <String, dynamic>{
            'action': 'get_storage_info',
            'description': 'Cannot get total space',
          }
        ],
      );
      expect(
          result,
          isA<StorageInfo>()
              .having(
                (StorageInfo p0) => p0.freeStorageInMegabytes,
                'test freeStorageInMegabytes',
                expectedFreeSpace,
              )
              .having(
                (StorageInfo p0) => p0.totalStorageInMegabytes,
                'test totalStorageInMegabytes',
                isNull,
              ));
    });
  });
}
