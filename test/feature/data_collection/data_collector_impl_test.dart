import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/data/request/mobile_data_collection_request.dart';
import 'package:flutter_common_package/feature/data_collection/data_collector_impl.dart';
import 'package:flutter_common_package/feature/data_collection/device_identifier/device_identifier.dart';
import 'package:flutter_common_package/feature/data_collection/ip_address_wrapper/ip_address_wrapper.dart';
import 'package:flutter_common_package/feature/data_collection/model/data_connection_type.dart';
import 'package:flutter_common_package/feature/data_collection/model/ip_address_info.dart';
import 'package:flutter_common_package/feature/data_collection/model/storage_info.dart';
import 'package:flutter_common_package/feature/data_collection/storage_info_wrapper/storage_info_wrapper.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockStorageInfoWrapper extends Mock implements StorageInfoWrapper {}

class MockConnectivity extends Mock implements Connectivity {}

class MockDeviceIdentifier extends Mock implements DeviceIdentifier {}

class MockIpAddressWrapper extends Mock implements IpAddressWrapper {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockFirebaseAnalyticsWrapper extends Mock implements FirebaseAnalyticsWrapper {}

void main() {
  final StorageInfoWrapper mockStorageInfoWrapper = MockStorageInfoWrapper();
  final Connectivity mockConnectivity = MockConnectivity();
  final DeviceIdentifier mockDeviceIdentifier = MockDeviceIdentifier();
  final IpAddressWrapper mockIpAddressWrapper = MockIpAddressWrapper();
  late LoggingRepo mockLoggingRepo;
  late FirebaseAnalyticsWrapper mockFirebaseAnalyticsWrapper;

  late DataCollectorImpl dataCollectorImpl;

  setUpAll(() {
    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    mockLoggingRepo = getIt.get<LoggingRepo>();

    getIt.registerLazySingleton<FirebaseAnalyticsWrapper>(() => MockFirebaseAnalyticsWrapper());
    mockFirebaseAnalyticsWrapper = getIt.get<FirebaseAnalyticsWrapper>();

    dataCollectorImpl = DataCollectorImpl(
        deviceIdentifier: mockDeviceIdentifier,
        storageInfoWrapper: mockStorageInfoWrapper,
        connectivity: mockConnectivity,
        ipAddressWrapper: mockIpAddressWrapper,
        loggingRepo: mockLoggingRepo,
        firebaseAnalyticsWrapper: mockFirebaseAnalyticsWrapper);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('Test getDeviceId', () {
    test('Should return right device id', () async {
      const String expectDeviceId = 'device_id';
      when(() => mockDeviceIdentifier.getDeviceId()).thenAnswer((_) async => expectDeviceId);
      final String? result = await dataCollectorImpl.getDeviceId();
      expect(result, expectDeviceId);
      verify(() => mockDeviceIdentifier.getDeviceId()).called(1);
    });
  });

  group('Test getStorageInfo', () {
    test('getStorageInfo should return the correct storage info', () async {
      final StorageInfo expectedStorageInfo = StorageInfo(
        freeStorageInMegabytes: 20,
        totalStorageInMegabytes: 100,
      );
      when(() => mockStorageInfoWrapper.getStorageInfo())
          .thenAnswer((_) async => expectedStorageInfo);

      final StorageInfo result = await dataCollectorImpl.getStorageInfo();
      expect(result, expectedStorageInfo);
      verify(() => mockStorageInfoWrapper.getStorageInfo()).called(1);
    });
  });

  group('Test getDataConnectionType', () {
    test(
        'getDataConnectionType should return type bluetooth when the device is connecting bluetooth',
        () async {
      when(() => mockConnectivity.checkConnectivity()).thenAnswer(
        (_) => Future<List<ConnectivityResult>>.value(<ConnectivityResult>[ConnectivityResult.bluetooth]),
      );

      final List<DataConnectionType>? result = await dataCollectorImpl.getDataConnectionType();

      expect(result, <DataConnectionType>[DataConnectionType.bluetooth]);
      verify(() => mockConnectivity.checkConnectivity()).called(1);
    });

    test('getDataConnectionType should return type wifi when the device is connecting wifi',
        () async {
      when(() => mockConnectivity.checkConnectivity()).thenAnswer(
        (_) => Future<List<ConnectivityResult>>.value(<ConnectivityResult>[ConnectivityResult.wifi]),
      );

      final List<DataConnectionType>? result = await dataCollectorImpl.getDataConnectionType();

      expect(result, <DataConnectionType>[DataConnectionType.wifi]);
      verify(() => mockConnectivity.checkConnectivity()).called(1);
    });

    test('getDataConnectionType should return type ethernet when the device is connecting ethernet',
        () async {
      when(() => mockConnectivity.checkConnectivity()).thenAnswer(
        (_) => Future<List<ConnectivityResult>>.value(<ConnectivityResult>[ConnectivityResult.ethernet]),
      );

      final List<DataConnectionType>? result = await dataCollectorImpl.getDataConnectionType();

      expect(result, <DataConnectionType>[DataConnectionType.ethernet]);
      verify(() => mockConnectivity.checkConnectivity()).called(1);
    });

    test('getDataConnectionType should return type mobile when the device is connecting mobile',
        () async {
      when(() => mockConnectivity.checkConnectivity()).thenAnswer(
        (_) => Future<List<ConnectivityResult>>.value(<ConnectivityResult>[ConnectivityResult.mobile]),
      );

      final List<DataConnectionType>? result = await dataCollectorImpl.getDataConnectionType();

      expect(result, <DataConnectionType>[DataConnectionType.mobile]);
      verify(() => mockConnectivity.checkConnectivity()).called(1);
    });

    test('getDataConnectionType should return type none when the device is connecting none',
        () async {
      when(() => mockConnectivity.checkConnectivity()).thenAnswer(
        (_) => Future<List<ConnectivityResult>>.value(<ConnectivityResult>[ConnectivityResult.none]),
      );

      final List<DataConnectionType>? result = await dataCollectorImpl.getDataConnectionType();

      expect(result, <DataConnectionType>[DataConnectionType.none]);
      verify(() => mockConnectivity.checkConnectivity()).called(1);
    });

    test('getDataConnectionType should return type vpn when the device is connecting vpn',
        () async {
      when(() => mockConnectivity.checkConnectivity()).thenAnswer(
        (_) => Future<List<ConnectivityResult>>.value(<ConnectivityResult>[ConnectivityResult.vpn]),
      );

      final List<DataConnectionType>? result = await dataCollectorImpl.getDataConnectionType();

      expect(result, <DataConnectionType>[DataConnectionType.vpn]);
      verify(() => mockConnectivity.checkConnectivity()).called(1);
    });

    test('getDataConnectionType should return type other when the device is connecting other',
        () async {
      when(() => mockConnectivity.checkConnectivity()).thenAnswer(
        (_) => Future<List<ConnectivityResult>>.value(<ConnectivityResult>[ConnectivityResult.other]),
      );

      final List<DataConnectionType>? result = await dataCollectorImpl.getDataConnectionType();

      expect(result, <DataConnectionType>[DataConnectionType.other]);
      verify(() => mockConnectivity.checkConnectivity()).called(1);
    });

    test('getDataConnectionType should return type other when the device is connecting other',
        () async {
      when(() => mockLoggingRepo.logErrorEvent(
            errorType: any(named: 'errorType'),
            args: any(named: 'args'),
          )).thenAnswer((_) => Future<void>.value());
      when(() => mockConnectivity.checkConnectivity()).thenAnswer(
        (_) => throw Exception('getDataConnectionType error'),
      );

      final List<DataConnectionType>? result = await dataCollectorImpl.getDataConnectionType();

      expect(result, isNull);
      verify(() => mockConnectivity.checkConnectivity()).called(1);
      expect(
        verify(() => mockLoggingRepo.logErrorEvent(
              errorType: captureAny(named: 'errorType'),
              args: captureAny(named: 'args'),
            )).captured,
        <dynamic>[
          'connectivity',
          <String, dynamic>{
            'action': 'get_data_connection_type',
            'description': 'Exception: getDataConnectionType error',
          }
        ],
      );
    });
  });

  group('Test function createMobileDataCollectionRequest', () {
    test('Give empty input, should return empty request', () {
      final MobileDataCollectionRequest result =
          dataCollectorImpl.createMobileDataCollectionRequest(<dynamic>[]);
      expect(result.toJson(), <String, dynamic>{
        'data_connection_type': null,
        'total_storage_of_device_in_mb': null,
        'free_storage_of_device_in_mb': null,
        'screen_resolution_of_device': null,
        'local_device_ip_address': null,
      });
    });

    test('Give all input, should return request with full data', () {
      const String expectLocalIpAddress = 'fake_local_ip';
      final MobileDataCollectionRequest result =
          dataCollectorImpl.createMobileDataCollectionRequest(<dynamic>[
        [DataConnectionType.bluetooth],
        StorageInfo(
          freeStorageInMegabytes: 20 * 1024,
          totalStorageInMegabytes: 64 * 1024,
        ),
        const Size(100, 200),
        IpAddressInfo(
          localDeviceIpAddress: expectLocalIpAddress,
        ),
      ]);

      expect(result.toJson(), <String, dynamic>{
        'data_connection_type': ['bluetooth'],
        'total_storage_of_device_in_mb': 65536,
        'free_storage_of_device_in_mb': 20480,
        'screen_resolution_of_device': '200 x 100',
        'local_device_ip_address': expectLocalIpAddress,
      });
    });
  });

  group('Test getIpAddress', () {
    test('getIpAddressInfo return null', () async {
      when(() => mockIpAddressWrapper.getIpAddressInfo()).thenAnswer((_) async => null);
      final IpAddressInfo? result = await dataCollectorImpl.getIpAddress();
      expect(result, isNull);
      verify(() => mockIpAddressWrapper.getIpAddressInfo()).called(1);
    });

    test('getIpAddressInfo return data', () async {
      const String expectLocalIpAddress = 'fake_local_ip';
      when(() => mockIpAddressWrapper.getIpAddressInfo()).thenAnswer((_) async {
        return IpAddressInfo(
          localDeviceIpAddress: 'fake_local_ip',
        );
      });
      final IpAddressInfo? result = await dataCollectorImpl.getIpAddress();
      expect(
          result,
          isA<IpAddressInfo>().having(
            (IpAddressInfo p0) => p0.localDeviceIpAddress,
            'test localDeviceIpAddress',
            expectLocalIpAddress,
          ));
      verify(() => mockIpAddressWrapper.getIpAddressInfo()).called(1);
    });
  });

  group('test getFirebaseAppInstanceId', () {
    test('getFirebaseAppInstanceId returns instance ID when successful', () async {
      const String instanceId = 'test_instance_id';
      when(() => mockFirebaseAnalyticsWrapper.getAppInstanceId())
          .thenAnswer((_) async => instanceId);

      final String? result = await dataCollectorImpl.getFirebaseAppInstanceId();

      expect(result, instanceId);
      verify(() => mockFirebaseAnalyticsWrapper.getAppInstanceId()).called(1);
    });

    test('getFirebaseAppInstanceId returns null when an exception occurs', () async {
      when(() => mockFirebaseAnalyticsWrapper.getAppInstanceId())
          .thenThrow(Exception('Failed to get instance ID'));

      final String? result = await dataCollectorImpl.getFirebaseAppInstanceId();

      expect(result, isNull);
      verify(() => mockFirebaseAnalyticsWrapper.getAppInstanceId()).called(1);
    });
  });
}
