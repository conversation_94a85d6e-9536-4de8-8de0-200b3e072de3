import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/feature/webview/webview_utils.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../init_common_package_test.dart';
import '../server_logging/event_tracking_utils_test.dart';

class CommonUtilFunctionMock extends Mock implements CommonUtilFunction {}

void main() {
  late CommonUtilFunction mockCommonUtilFunction;
  late CommonWebViewUtils commonWebViewUtils;

  setUpAll(() {
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunctionMock());

    commonWebViewUtils = CommonWebViewUtils();

    mockCommonUtilFunction = getIt.get<CommonUtilFunction>();

    when(() => mockCommonUtilFunction.commonLaunchUrlString(
          any(),
          mode: CommonLaunchUrlMode.externalApplication,
        )).thenAnswer((_) => Future<void>.value());
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('verify getNavigationActionPolicy return NavigationActionPolicy value', () {
    test('should_cancel_null_url', () async {
      final NavigationAction navigationAction =
          NavigationAction(request: URLRequest(method: 'GET', url: null), isForMainFrame: true);

      expect(
          await commonWebViewUtils.getNavigationActionPolicy(
              navigationAction: navigationAction,
              originalUrl: 'originUrl',
              redirectType: WebViewRedirectType.inApp),
          NavigationActionPolicy.CANCEL);
    });

    test('should_cancel_with_null_url', () async {
      final NavigationAction navigationAction =
          NavigationAction(request: URLRequest(method: 'GET', url: null), isForMainFrame: false);

      expect(
          await commonWebViewUtils.getNavigationActionPolicy(
              navigationAction: navigationAction,
              originalUrl: 'originUrl',
              redirectType: WebViewRedirectType.inApp),
          NavigationActionPolicy.CANCEL);
    });

    test('should_allow_redirect_to_the_same_url_as_origin', () async {
      const String originalUrl = 'originalUrl';
      final NavigationAction navigationAction = NavigationAction(
          request: URLRequest(method: 'GET', url: WebUri(originalUrl)), isForMainFrame: true);

      expect(
          await commonWebViewUtils.getNavigationActionPolicy(
              navigationAction: navigationAction,
              originalUrl: originalUrl,
              redirectType: WebViewRedirectType.cancel),
          NavigationActionPolicy.ALLOW);
    });

    test('should_cancel_following_config_if_different_from_origin_url', () async {
      const String originalUrl = 'originalUrl';
      const String redirectUrl = 'redirectUrl';

      final NavigationAction navigationAction = NavigationAction(
          request: URLRequest(method: 'GET', url: WebUri(redirectUrl)), isForMainFrame: true);

      expect(
          await commonWebViewUtils.getNavigationActionPolicy(
              navigationAction: navigationAction,
              originalUrl: originalUrl,
              redirectType: WebViewRedirectType.cancel),
          NavigationActionPolicy.CANCEL);
    });

    test('should_allow_if_config_is_open_redirect_url_not_http_by_WebView', () async {
      const String originalUrl = 'originalUrl';
      const String redirectUrl = 'redirectUrl';

      when(() => mockCommonUtilFunction.commonLaunchUrlString(any()))
          .thenAnswer((_) => Future<void>.value());

      final NavigationAction navigationAction = NavigationAction(
          request: URLRequest(method: 'GET', url: WebUri(redirectUrl)), isForMainFrame: true);

      expect(
          await commonWebViewUtils.getNavigationActionPolicy(
              navigationAction: navigationAction,
              originalUrl: originalUrl,
              redirectType: WebViewRedirectType.inApp),
          NavigationActionPolicy.CANCEL);

      verify(() => mockCommonUtilFunction.commonLaunchUrlString(redirectUrl)).called(1);
    });

    test('should_allow_if_config_is_open_redirect_url_with_http_link_by_WebView', () async {
      const String originalUrl = 'originalUrl';
      const String redirectUrl = 'https://redirectUrl';

      final NavigationAction navigationAction = NavigationAction(
          request: URLRequest(method: 'GET', url: WebUri(redirectUrl)), isForMainFrame: true);

      expect(
          await commonWebViewUtils.getNavigationActionPolicy(
              navigationAction: navigationAction,
              originalUrl: originalUrl,
              redirectType: WebViewRedirectType.inApp),
          NavigationActionPolicy.ALLOW);

      verifyNever(() => mockCommonUtilFunction.commonLaunchUrlString(redirectUrl,
          mode: CommonLaunchUrlMode.externalApplication));
    });

    test('should_cancel_if_config_is_open_redirect_url_by_external_browser', () async {
      const String originalUrl = 'originalUrl';
      const String redirectUrl = 'redirectUrl';

      TestWidgetsFlutterBinding.ensureInitialized();

      final NavigationAction navigationAction = NavigationAction(
          request: URLRequest(method: 'GET', url: WebUri(redirectUrl)), isForMainFrame: true);

      expect(
          await commonWebViewUtils.getNavigationActionPolicy(
              navigationAction: navigationAction,
              originalUrl: originalUrl,
              redirectType: WebViewRedirectType.externalBrowser),
          NavigationActionPolicy.CANCEL);

      verify(() => mockCommonUtilFunction.commonLaunchUrlString(redirectUrl,
          mode: CommonLaunchUrlMode.externalApplication)).called(1);
    });

    test('should_cancel_if_config_is_cancel', () async {
      const String originalUrl = 'originalUrl';
      const String redirectUrl = 'redirectUrl';

      final NavigationAction navigationAction = NavigationAction(
          request: URLRequest(method: 'GET', url: WebUri(redirectUrl)), isForMainFrame: true);

      expect(
          await commonWebViewUtils.getNavigationActionPolicy(
              navigationAction: navigationAction,
              originalUrl: originalUrl,
              redirectType: WebViewRedirectType.cancel),
          NavigationActionPolicy.CANCEL);
    });

    test('should cancel navigation and invoke callback if redirectUrl is startWiths deeplink',
        () async {
      when(() => mockCommonUtilFunction.commonLaunchUrlString(
            any(),
            mode: CommonLaunchUrlMode.inAppWebView,
          )).thenAnswer((_) => Future<void>.value());

      final RegExp specialLinkFormat = RegExp(r'^evo:\/\/deeplink.*$');
      const String originalUrl = 'originalUrl';
      const String redirectUrl = 'evo://deeplink.evo-native?screen=xyz';
      bool hasCallDeeplinkNavigationDetected = false;

      final NavigationAction navigationAction = NavigationAction(
        request: URLRequest(method: 'GET', url: WebUri(redirectUrl)),
        isForMainFrame: true,
      );
      final NavigationActionPolicy policy = await commonWebViewUtils.getNavigationActionPolicy(
        navigationAction: navigationAction,
        originalUrl: originalUrl,
        redirectType: WebViewRedirectType.inApp,
        onHandleSpecialLink: (String deeplink) {
          hasCallDeeplinkNavigationDetected = true;
        },
        formatSpecialLink: specialLinkFormat,
      );
      // verify
      expect(policy, NavigationActionPolicy.CANCEL);
      expect(hasCallDeeplinkNavigationDetected, true);
    });

    test('should cancel navigation and do nothing if redirectUrl is not startWiths deeplink',
        () async {
      final RegExp specialLinkFormat = RegExp(r'^evo:\/\/deeplink.*$');
      const String originalUrl = 'originalUrl';
      const String redirectUrl = 'redirectUrl';
      bool hasCallDeeplinkNavigationDetected = false;

      final NavigationAction navigationAction = NavigationAction(
        request: URLRequest(method: 'GET', url: WebUri(redirectUrl)),
        isForMainFrame: true,
      );
      final NavigationActionPolicy policy = await commonWebViewUtils.getNavigationActionPolicy(
        navigationAction: navigationAction,
        originalUrl: originalUrl,
        redirectType: WebViewRedirectType.inApp,
        onHandleSpecialLink: (String deeplink) {
          hasCallDeeplinkNavigationDetected = true;
        },
        formatSpecialLink: specialLinkFormat,
      );
      // verify
      expect(policy, NavigationActionPolicy.CANCEL);
      expect(hasCallDeeplinkNavigationDetected, false);
    });
  });

  group('verify canBeOpenByWebView() method', () {
    test('check canBeOpenByWebView = true with https', () {
      const String url = 'https://www.google.com/';
      final bool isSchemeHttp = commonWebViewUtils.canBeOpenByWebView(url);
      expect(isSchemeHttp, true);
    });

    test('check canBeOpenByWebView = true with http', () {
      const String url = 'http://www.google.com/';
      final bool isSchemeHttp = commonWebViewUtils.canBeOpenByWebView(url);
      expect(isSchemeHttp, true);
    });

    test('check canBeOpenByWebView = false', () {
      const String url = 'mailto:<EMAIL>';
      final bool isSchemeHttp = commonWebViewUtils.canBeOpenByWebView(url);
      expect(isSchemeHttp, false);
    });
  });

  group('verify useHybridComposition method', () {
    final DevicePlatform mockDevicePlatform = MockDevicePlatform();
    final DeviceInfoPluginWrapper mockDeviceInfoPluginWrapper = MockDeviceInfoPluginWrapper();
    setUpAll(() {
      getIt.registerLazySingleton<DevicePlatform>(() => mockDevicePlatform);
      getIt.registerLazySingleton<DeviceInfoPluginWrapper>(() => mockDeviceInfoPluginWrapper);
    });

    tearDownAll(() {
      getIt.unregister<DevicePlatform>();
      getIt.unregister<DeviceInfoPluginWrapper>();
    });
    test('check useHybridComposition should return true if device os is Android 10', () {
      when(() => mockDevicePlatform.isAndroid()).thenAnswer((_) => true);
      when(() => mockDeviceInfoPluginWrapper.getOSVersion()).thenAnswer((_) => '29');
      expect(commonWebViewUtils.useHybridComposition, true);
    });
    test('check useHybridComposition should return false if device os is Android bigger than 10', () {
      when(() => mockDevicePlatform.isAndroid()).thenAnswer((_) => true);
      when(() => mockDeviceInfoPluginWrapper.getOSVersion()).thenAnswer((_) => '30');
      expect(commonWebViewUtils.useHybridComposition, false);
    });
    test('check useHybridComposition should return false if device os is Android bigger than 10', () {
      when(() => mockDevicePlatform.isAndroid()).thenAnswer((_) => true);
      when(() => mockDeviceInfoPluginWrapper.getOSVersion()).thenAnswer((_) => '31');
      expect(commonWebViewUtils.useHybridComposition, false);
    });
    test('check useHybridComposition should return false if device os is Android smaller than 10', () {
      when(() => mockDevicePlatform.isAndroid()).thenAnswer((_) => true);
      when(() => mockDeviceInfoPluginWrapper.getOSVersion()).thenAnswer((_) => '28');
      expect(commonWebViewUtils.useHybridComposition, false);
    });
    test('check useHybridComposition should return false if device os is Android smaller than 10', () {
      when(() => mockDevicePlatform.isAndroid()).thenAnswer((_) => true);
      when(() => mockDeviceInfoPluginWrapper.getOSVersion()).thenAnswer((_) => '27');
      expect(commonWebViewUtils.useHybridComposition, false);
    });
    test('check useHybridComposition should return false if device is iOS', () {
      when(() => mockDevicePlatform.isAndroid()).thenAnswer((_) => false);
      when(() => mockDeviceInfoPluginWrapper.getOSVersion()).thenAnswer((_) => 'ios 13.0');
      expect(commonWebViewUtils.useHybridComposition, false);
    });
  });
}
