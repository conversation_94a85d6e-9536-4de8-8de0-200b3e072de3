import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_core_platform_interface/firebase_core_platform_interface.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/ekyc/ekyc_repo.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/data_collection/data_collector.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/ekyc_bridge.dart';
import 'package:flutter_common_package/feature/in_app_update/common_in_app_update.dart';
import 'package:flutter_common_package/feature/onesignal/listener_handler.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_crashlytics.dart';
import 'package:flutter_common_package/feature/webview/webview_utils.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/global.dart';
import 'package:flutter_common_package/util/alert_manager.dart';
import 'package:flutter_common_package/util/clear_all_notifications_wrapper.dart';
import 'package:flutter_common_package/util/clipboard_wrapper.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/flutter_downloader/common_flutter_downloader.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:flutter_common_package/util/otp_auto_fill/otp_auto_fill.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_common_package/util/url_launcher_wrapper.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/util/uuid/uuid_generator.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:package_info_plus/package_info_plus.dart';

// ignore: avoid_implementing_value_types
class MockPackageInfo extends Mock implements PackageInfo {}

class MockDeviceInfoPlugin extends Mock implements DeviceInfoPlugin {}

class MockAndroidDeviceInfo extends Mock implements AndroidDeviceInfo {}

class MockIosDeviceInfo extends Mock implements IosDeviceInfo {}

class MockFlavorConfig extends Mock implements FlavorConfig {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

class MockIosUtsname extends Mock implements IosUtsname {}

class MockAndroidBuildVersion extends Mock implements AndroidBuildVersion {}

class MockDataCollector extends Mock implements DataCollector {}

class MockFirebasePlatform extends Mock implements FirebasePlatform {}

class MockFirebaseOptions extends Mock implements FirebaseOptions {}

class MockFirebaseCrashlytics extends Mock implements FirebaseCrashlytics {}

class MockDatadogSdk extends Mock implements DatadogSdk {}

class MockDatadogLogger extends Mock implements DatadogLogger {}

class MockDatadogLogging extends Mock implements DatadogLogging {}

class MockOneSignal extends Mock implements OneSignal {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockOneSignalListenerHandler extends Mock implements OneSignalListenerHandler {}

class MockOSPermissionStateChanges extends Mock implements OSPermissionStateChanges {
  @override
  String jsonRepresentation() => '{"to": {"status": 2}}';
}

class MockOSPermissionState extends Mock implements OSPermissionState {
  @override
  OSNotificationPermission? status = OSNotificationPermission.denied;
}

class MockOSSubscriptionStateChanges extends Mock implements OSSubscriptionStateChanges {
  @override
  String jsonRepresentation() => '{"to": {"pushToken": "test-token"}}';
}

class MockOSSubscriptionState extends Mock implements OSSubscriptionState {
  @override
  String? pushToken = 'test-token';
}

void main() {
  late PackageInfo mockPackageInfo;
  late DeviceInfoPlugin mockDeviceInfoPlugin;
  late DevicePlatform mockDevicePlatform;
  late DeviceInfoPluginWrapper deviceInfoPluginWrapper;
  late DataCollector dataCollector;

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();

    _setupMockData();

    FlavorConfig(
        flavor: 'stag',
        values: CommonFlavorValues(
          baseUrl: 'https://example.com',
          initializeFirebaseSdk: true,
          oneSignalAppId: null,
        ));
  });

  group('verify all dependencies are injected to GetIt', () {
    setUpAll(() async {
      await initCommonPackage();
    });

    tearDownAll(() {
      _tearDownMockData();
    });

    test('verify all dependencies are injected to GetIt', () async {
      expect(getIt.isRegistered<DevicePlatform>(), true);
      expect(getIt.isRegistered<PackageInfo>(), true);
      expect(getIt.isRegistered<DeviceInfoPlugin>(), true);
      expect(getIt.isRegistered<DeviceInfoPluginWrapper>(), true);
      expect(getIt.isRegistered<DataCollector>(), true);
      expect(getIt.isRegistered<FirebaseAnalyticsWrapper>(), true);
      expect(getIt.isRegistered<Connectivity>(), true);
      expect(getIt.isRegistered<NetworkManager>(), true);
      expect(getIt.isRegistered<FlutterSecureStorage>(), true);
      expect(getIt.isRegistered<CommonLocalStorageHelper>(), true);

      final CommonLocalStorageHelper localStorageHelper = getIt.get<CommonLocalStorageHelper>();
      expect(
          localStorageHelper,
          isA<CommonSecureStorageHelperImpl>().having(
              (CommonSecureStorageHelperImpl p) => p.secureStorage,
              'verify secureStorage',
              isA<FlutterSecureStorage>()));

      expect(getIt.isRegistered<CommonSharedPreferencesHelper>(), true);
      expect(getIt.isRegistered<CommonUtilFunction>(), true);
      expect(getIt.isRegistered<Dio>(), true);
      expect(getIt.isRegistered<CommonHttpClient>(), true);
      expect(getIt.isRegistered<LoggingRepo>(), true);
      expect(getIt.isRegistered<UUIDGenerator>(), true);
      expect(getIt.isRegistered<EventTrackingUtils>(), true);
      expect(getIt.isRegistered<AlertManager>(), true);
      expect(getIt.isRegistered<CommonImageProvider>(), true);
      expect(getIt.isRegistered<CommonNavigatorObserver>(), true);
      expect(getIt.isRegistered<OtpAutoFill>(), true);
      expect(getIt.isRegistered<ClipboardWrapper>(), true);
      expect(getIt.isRegistered<UrlLauncherWrapper>(), true);
      expect(getIt.isRegistered<ClearAllNotificationsWrapper>(), true);
      expect(getIt.isRegistered<CommonFlutterDownloader>(), true);
      expect(getIt.isRegistered<GlobalKeyProvider>(), true);
      expect(getIt.isRegistered<CommonWebViewUtils>(), true);
      expect(getIt.isRegistered<EkycBridge>(), true);
      expect(getIt.isRegistered<EkycRepo>(), true);
      expect(getIt.isRegistered<InAppUpdateWrapper>(), true);
    });
  });

  group('getDio', () {
    setUpAll(() async {
      await getIt.reset();

      _setupMockData();

      getIt.registerLazySingleton<DevicePlatform>(() => MockDevicePlatform());
      getIt.registerLazySingletonAsync<PackageInfo>(() async => MockPackageInfo());
      getIt.registerLazySingleton<DeviceInfoPlugin>(() => MockDeviceInfoPlugin());
      getIt.registerLazySingleton<DeviceInfoPluginWrapper>(() => DeviceInfoPluginWrapper(
            deviceInfo: getIt.get<DeviceInfoPlugin>(),
            platform: getIt.get<DevicePlatform>(),
          ));

      mockDevicePlatform = getIt.get<DevicePlatform>();
      mockPackageInfo = await getIt.getAsync<PackageInfo>();
      mockDeviceInfoPlugin = getIt.get<DeviceInfoPlugin>();
      deviceInfoPluginWrapper = getIt.get<DeviceInfoPluginWrapper>();
    });

    tearDown(() {
      reset(mockDevicePlatform);
      reset(mockPackageInfo);
      reset(mockDeviceInfoPlugin);
      deviceInfoPluginWrapper.androidInfo = null;
      deviceInfoPluginWrapper.iosInfo = null;
      reset(dataCollector);
    });

    setUp(() {
      if (!getIt.isRegistered<DataCollector>()) {
        dataCollector = getIt.registerSingleton<DataCollector>(MockDataCollector());
      }
    });

    test('should return Dio instance with correct headers for Android', () async {
      final MockAndroidDeviceInfo mockAndroidDeviceInfo = MockAndroidDeviceInfo();
      final MockAndroidBuildVersion mockAndroidBuildVersion = MockAndroidBuildVersion();

      // Arrange
      when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
      when(() => mockDevicePlatform.isIOS()).thenReturn(false);
      when(() => mockPackageInfo.version).thenReturn('1.0.0');
      when(() => mockPackageInfo.buildNumber).thenReturn('123');
      deviceInfoPluginWrapper.androidInfo = mockAndroidDeviceInfo;
      when(() => mockAndroidBuildVersion.sdkInt).thenReturn(29);
      when(() => mockAndroidDeviceInfo.id).thenReturn('android_build_number');
      when(() => mockAndroidDeviceInfo.manufacturer).thenReturn('Samsung');
      when(() => mockAndroidDeviceInfo.model).thenReturn('Galaxy S10');
      when(() => mockAndroidDeviceInfo.version).thenReturn(mockAndroidBuildVersion);
      when(() => mockAndroidBuildVersion.sdkInt).thenReturn(29);
      when(() => dataCollector.getDeviceId()).thenAnswer((_) async => 'device_id_test');

      // Act
      final Dio dio = await getDio();

      // Assert
      expect(dio, isA<Dio>());
      expect(dio.options.headers[HeaderKey.appVersion], '1.0.0');
      expect(dio.options.headers[HeaderKey.appBuildNumber], '123');
      expect(dio.options.headers[HeaderKey.osBuildNumber], 'android_build_number');
      expect(dio.options.headers[HeaderKey.deviceModel], 'Samsung Galaxy S10');
      expect(dio.options.headers[HeaderKey.platform], 'android');
      expect(dio.options.headers[HeaderKey.osVersion], '29');
      expect(dio.options.headers[HeaderKey.language], isNull);
      expect(dio.options.headers[HeaderKey.deviceId], 'device_id_test');
      expect(dio.options.baseUrl, 'https://example.com');
    });

    test('should return Dio instance with correct headers for iOS', () async {
      final MockIosDeviceInfo mockIosDeviceInfo = MockIosDeviceInfo();
      final MockIosUtsname mockIosUtsname = MockIosUtsname();

      // Arrange
      when(() => mockDevicePlatform.isIOS()).thenReturn(true);
      when(() => mockDevicePlatform.isAndroid()).thenReturn(false);
      when(() => mockPackageInfo.version).thenReturn('1.0.0');
      when(() => mockPackageInfo.buildNumber).thenReturn('123');
      when(() => mockIosDeviceInfo.utsname).thenReturn(mockIosUtsname);
      when(() => mockIosUtsname.machine).thenReturn('iPhone X');
      when(() => mockIosDeviceInfo.systemVersion).thenReturn('14.0');
      when(() => dataCollector.getDeviceId()).thenAnswer((_) async => 'device_id_test');
      deviceInfoPluginWrapper.iosInfo = mockIosDeviceInfo;

      // Act
      final Dio dio = await getDio(locale: const Locale('en'));

      // Assert
      expect(dio, isA<Dio>());
      expect(dio.options.headers[HeaderKey.appVersion], '1.0.0');
      expect(dio.options.headers[HeaderKey.appBuildNumber], '123');
      expect(dio.options.headers[HeaderKey.deviceModel], 'iPhone X');
      expect(dio.options.headers[HeaderKey.platform], 'ios');
      expect(dio.options.headers[HeaderKey.osVersion], '14.0');
      expect(dio.options.headers[HeaderKey.language], 'en');
      expect(dio.options.headers[HeaderKey.deviceId], 'device_id_test');
      expect(dio.options.baseUrl, 'https://example.com');
    });

    test('should have no device ID when the DataCollector is not registered', () async {
      getIt.unregister<DataCollector>();

      final MockIosDeviceInfo mockIosDeviceInfo = MockIosDeviceInfo();
      final MockIosUtsname mockIosUtsname = MockIosUtsname();

      when(() => mockDevicePlatform.isIOS()).thenReturn(true);
      when(() => mockDevicePlatform.isAndroid()).thenReturn(false);
      when(() => mockPackageInfo.version).thenReturn('');
      when(() => mockPackageInfo.buildNumber).thenReturn('');
      when(() => mockIosUtsname.machine).thenReturn('');
      deviceInfoPluginWrapper.iosInfo = mockIosDeviceInfo;
      when(() => mockIosDeviceInfo.utsname).thenReturn(mockIosUtsname);
      when(() => mockIosDeviceInfo.systemVersion).thenReturn('');

      final Dio dio = await getDio();

      expect(dio, isA<Dio>());
      verifyNever(() => dataCollector.getDeviceId());
      expect(dio.options.headers[HeaderKey.deviceId], isNull);
    });
  });

  group('test _initDataDog', () {
    late MockDatadogSdk mockDatadogSdk;
    late MockDatadogLogger mockDatadogLogger;
    late MockDatadogLogging mockDatadogLogging;
    late MockDataCollector mockDataCollector;

    setUpAll(() {
      registerFallbackValue(DatadogConfiguration(clientToken: '', env: '', site: DatadogSite.us1));
      registerFallbackValue(DatadogLoggerConfiguration());
      registerFallbackValue(TrackingConsent.granted);
    });

    setUp(() {
      mockDatadogSdk = MockDatadogSdk();
      mockDatadogLogger = MockDatadogLogger();
      mockDatadogLogging = MockDatadogLogging();
      mockDataCollector = MockDataCollector();
      when(() => mockDatadogSdk.initialize(any(), any()))
          .thenAnswer((_) async => Future<void>.value());
      when(() => mockDatadogSdk.logs).thenAnswer((_) => mockDatadogLogging);
      when(() => mockDatadogLogging.createLogger(any())).thenAnswer((_) => mockDatadogLogger);
      when(() => mockDatadogLogger.error(any(), attributes: any(named: 'attributes')))
          .thenReturn(null);
      getIt.registerLazySingleton<DataCollector>(() => mockDataCollector);
      when(() => mockDataCollector.getDeviceId()).thenAnswer((_) async => '');
    });

    tearDown(() {
      getIt.reset();
    });

    test('should initialize DatadogSdk when datadogConfiguration is provided', () async {
      // Arrange
      final DatadogConfiguration testDatadogConfiguration = DatadogConfiguration(
        clientToken: 'testClientToken',
        env: 'test',
        site: DatadogSite.us1,
        loggingConfiguration: DatadogLoggingConfiguration(),
      );

      final CommonDataDogConfig testCommonDataDogConfig = CommonDataDogConfig(
        datadogConfiguration: testDatadogConfiguration,
      );

      FlavorConfig(
        flavor: 'test',
        values: CommonFlavorValues(
          baseUrl: 'https://example.com',
          initializeFirebaseSdk: true,
          oneSignalAppId: null,
          commonDataDogConfig: testCommonDataDogConfig,
        ),
      );

      // Act
      await initDataDog(mockDatadogSdk);

      // Assert
      verify(() => mockDatadogSdk.initialize(testDatadogConfiguration, TrackingConsent.granted))
          .called(1);
      verify(() => mockDatadogSdk.logs!.createLogger(any())).called(1);
      expect(getIt.isRegistered<DatadogLogger>(), true);
    });

    test('should initialize DatadogSdk when datadogConfiguration is not provided', () {
      FlavorConfig(
        flavor: 'test',
        values: CommonFlavorValues(
          baseUrl: 'https://example.com',
          initializeFirebaseSdk: true,
          oneSignalAppId: null,
        ),
      );

      // Act
      initDataDog(mockDatadogSdk);

      // Assert
      verifyNever(() => mockDatadogSdk.initialize(any(), any()));
    });
  });

  group('test init one signal', () {
    late OneSignal mockOneSignal;
    late MockLoggingRepo mockLoggingRepo;
    late MockOneSignalListenerHandler mockOneSignalListenerHandler;

    setUpAll(() {
      registerFallbackValue(OSLogLevel.none);
      registerFallbackValue(EventType.deniedNotification);
      registerFallbackValue(EventType.notificationToken);
      registerFallbackValue(OSPermissionStateChanges);
      registerFallbackValue(OSSubscriptionStateChanges(<String, dynamic>{}));
    });

    setUp(() {
      mockOneSignal = MockOneSignal();
      mockLoggingRepo = MockLoggingRepo();
      mockOneSignalListenerHandler = MockOneSignalListenerHandler();

      // Replace static instances with mocks
      OneSignal.shared = mockOneSignal;
      // Reset GetIt
      getIt.reset();

      // Register mocks with GetIt
    });

    tearDown(() {
      getIt.reset();
    });

    test('should return early when oneSignalAppId is null', () async {
      // Act
      await initOneSignal();

      // Assert
      verifyNever(() => mockOneSignal.setLogLevel(any(), any()));
      verifyNever(() => mockOneSignal.setLocationShared(any()));
      verifyNever(() => mockOneSignal.setAppId(any()));
    });

    test('should initialize OneSignal when oneSignalAppId is provided', () async {
      // Arrange
      FlavorConfig(
          flavor: 'stag',
          values: CommonFlavorValues(
            baseUrl: 'https://example.com',
            initializeFirebaseSdk: true,
            oneSignalAppId: 'test-app-id',
          ));
      when(() => mockOneSignal.setLogLevel(any(), any())).thenAnswer((_) async {});
      when(() => mockOneSignal.setLocationShared(any())).thenAnswer((_) async {});
      when(() => mockOneSignal.setAppId(any())).thenAnswer((_) async {});
      when(() => mockOneSignal.setLaunchURLsInApp(any())).thenAnswer((_) async {});
      when(() => mockOneSignal.disablePush(any())).thenAnswer((_) async {});
      when(() => mockOneSignal.setPermissionObserver(any())).thenAnswer((_) async {});
      when(() => mockOneSignal.setSubscriptionObserver(any())).thenAnswer((_) async {});

      // Act
      await initOneSignal();

      // Assert
      verify(() => mockOneSignal.setLogLevel(any(), any())).called(1);
      verify(() => mockOneSignal.setLocationShared(false));
      verify(() => mockOneSignal.setAppId('test-app-id'));
      verify(() => mockOneSignal.setLaunchURLsInApp(false));
      verify(() => mockOneSignal.disablePush(false));
      verify(() => mockOneSignal.setPermissionObserver(any()));
      verify(() => mockOneSignal.setSubscriptionObserver(any()));
    });

    test('should log event when notification permission is denied', () async {
      // Arrange
      getIt.registerSingleton<LoggingRepo>(mockLoggingRepo);

      FlavorConfig(
          flavor: 'stag',
          values: CommonFlavorValues(
            baseUrl: 'https://example.com',
            initializeFirebaseSdk: true,
            oneSignalAppId: 'test-app-id',
          ));

      when(() => mockLoggingRepo.logEvent(
            eventType: EventType.deniedNotification,
            data: any(named: 'data'),
          )).thenAnswer((_) => Future<void>.value());

      when(() => mockOneSignal.setLogLevel(any(), any())).thenAnswer((_) async {});
      when(() => mockOneSignal.setLocationShared(any())).thenAnswer((_) async {});
      when(() => mockOneSignal.setAppId(any())).thenAnswer((_) async {});
      when(() => mockOneSignal.setLaunchURLsInApp(any())).thenAnswer((_) async {});
      when(() => mockOneSignal.disablePush(any())).thenAnswer((_) async {});

      // Capture the permission observer to trigger it manually
      void Function(OSPermissionStateChanges)? permissionObserver;
      when(() => mockOneSignal.setPermissionObserver(any())).thenAnswer((Invocation invocation) {
        permissionObserver = invocation.positionalArguments[0];
      });

      when(() => mockOneSignal.setSubscriptionObserver(any())).thenAnswer((_) async {});

      when(() => mockLoggingRepo.logEvent(
            eventType: any(named: 'eventType'),
            data: any(named: 'data'),
          )).thenAnswer((_) async {});

      // Act
      await initOneSignal();

      // Create mock permission changes with denied status
      final MockOSPermissionStateChanges mockChanges = MockOSPermissionStateChanges();
      final MockOSPermissionState mockPermissionState = MockOSPermissionState();
      when(() => mockChanges.to).thenReturn(mockPermissionState);

      // Trigger the permission observer
      permissionObserver?.call(mockChanges);

      // Assert
      verify(() => mockLoggingRepo.logEvent(
            eventType: EventType.deniedNotification,
            data: any(named: 'data'),
          ));
    });

    test('should handle subscription changes and log notification token', () async {
      // Arrange
      getIt.registerSingleton<LoggingRepo>(mockLoggingRepo);

      FlavorConfig(
          flavor: 'stag',
          values: CommonFlavorValues(
            baseUrl: 'https://example.com',
            initializeFirebaseSdk: true,
            oneSignalAppId: 'test-app-id',
          ));

      when(() => mockLoggingRepo.logEvent(
            eventType: EventType.deniedNotification,
            data: any(named: 'data'),
          )).thenAnswer((_) => Future<void>.value());
      when(() => mockOneSignal.setLogLevel(any(), any())).thenAnswer((_) async {});
      when(() => mockOneSignal.setLocationShared(any())).thenAnswer((_) async {});
      when(() => mockOneSignal.setAppId(any())).thenAnswer((_) async {});
      when(() => mockOneSignal.setLaunchURLsInApp(any())).thenAnswer((_) async {});
      when(() => mockOneSignal.disablePush(any())).thenAnswer((_) async {});
      when(() => mockOneSignal.setPermissionObserver(any())).thenAnswer((_) async {});

      // Capture the subscription observer to trigger it manually
      void Function(OSSubscriptionStateChanges)? subscriptionObserver;
      when(() => mockOneSignal.setSubscriptionObserver(any())).thenAnswer((Invocation invocation) {
        subscriptionObserver = invocation.positionalArguments[0];
      });

      when(() => mockLoggingRepo.logEvent(
            eventType: any(named: 'eventType'),
            data: any(named: 'data'),
          )).thenAnswer((_) async {});

      // Act
      await initOneSignal();

      // Register handler after initialization to test the conditional branch
      getIt.registerSingleton<OneSignalListenerHandler>(mockOneSignalListenerHandler);
      when(() => mockOneSignalListenerHandler.onSubscriptionObserver(any())).thenAnswer((_) {});

      // Create mock subscription changes
      final MockOSSubscriptionStateChanges mockChanges = MockOSSubscriptionStateChanges();
      final MockOSSubscriptionState mockSubscriptionState = MockOSSubscriptionState();
      when(() => mockChanges.to).thenReturn(mockSubscriptionState);

      // Trigger the subscription observer
      subscriptionObserver?.call(mockChanges);

      // Assert
      verify(() => mockLoggingRepo.logEvent(
            eventType: EventType.notificationToken,
            data: <String, dynamic>{'token': 'test-token'},
          ));

      verify(() => mockOneSignalListenerHandler.onSubscriptionObserver(mockChanges));
    });
  });
}

void _setupMockData() {
  // firebase
  final FirebasePlatform mockFirebase = MockFirebasePlatform();
  Firebase.delegatePackingProperty = mockFirebase;
  when(() => mockFirebase.initializeApp(name: any(named: 'name'), options: any(named: 'options')))
      .thenAnswer((_) async => FirebaseAppPlatform(defaultFirebaseAppName, MockFirebaseOptions()));
  when(() => mockFirebase.app(any()))
      .thenAnswer((_) => FirebaseAppPlatform(defaultFirebaseAppName, MockFirebaseOptions()));

  // crashlytics
  final FirebaseCrashlytics mockFirebaseCrashlytics = MockFirebaseCrashlytics();
  FirebaseCrashlyticsWrapper.instanceForTesting = mockFirebaseCrashlytics;

  when(
    () => mockFirebaseCrashlytics.setCustomKey(HeaderKey.deviceId, any()),
  ).thenAnswer((_) async {});

  // mock channels
  for (final _MockChannel mockChannel in _MockChannel.values) {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      MethodChannel(mockChannel.channelName),
      (MethodCall methodCall) async {
        return mockChannel.data;
      },
    );
  }
}

void _tearDownMockData() {
  FirebaseCrashlyticsWrapper.resetToOriginalInstance();

  Firebase.delegatePackingProperty = null;
  //mock channels
  for (final _MockChannel mockChannel in _MockChannel.values) {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      MethodChannel(mockChannel.channelName),
      null,
    );
  }
}

enum _MockChannel {
  oneSignal('OneSignal'),
  downloader('vn.hunghd/downloader'),
  packageInfo('dev.fluttercommunity.plus/package_info', <String, dynamic>{}),
  appsFlyerCallBacks('callbacks'),
  appsFlyerAfApi('af-api'),
  firebasePerformance('plugins.flutter.io/firebase_performance'),
  sensorPlusMethod('dev.fluttercommunity.plus/sensors/method'),
  sensorPlusAccelerometer('dev.fluttercommunity.plus/sensors/accelerometer'),
  connectivity('dev.fluttercommunity.plus/connectivity'),
  connectivityStatus('dev.fluttercommunity.plus/connectivity_status'),
  deviceInfo('dev.fluttercommunity.plus/device_info', <String, dynamic>{
    'version': <String, dynamic>{
      'sdkInt': 30,
      'baseOS': 'baseOS',
      'codename': 'codename',
      'incremental': 'incremental',
      'previewSdkInt': 30,
      'release': 'release',
      'securityPatch': 'securityPatch',
    },
    'board': '.board',
    'bootloader': '.bootloader',
    'brand': '.brand',
    'device': '.device',
    'display': '.display',
    'fingerprint': '.fingerprint',
    'hardware': '.hardware',
    'host': '.host',
    'id': '.id',
    'manufacturer': '.manufacturer',
    'model': '.model',
    'product': '.product',
    'supported32BitAbis': <String>[],
    'supported64BitAbis': <String>[],
    'supportedAbis': <String>[],
    'tags': '.tags',
    'type': '.type',
    'isPhysicalDevice': false,
    'systemFeatures': <String>[],
    'serialNumber': 'serialNumber',
    'isLowRamDevice': false,
  });

  final String channelName;
  final dynamic data;

  const _MockChannel(this.channelName, [this.data]);
}
